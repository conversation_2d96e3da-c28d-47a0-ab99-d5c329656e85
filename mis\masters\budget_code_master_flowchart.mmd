flowchart TD
    A[User Access MIS Budget Code Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Budget Code Master Page]
    
    D --> E[Display Budget Code Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblMIS_BudgetCode]
    G --> H[Display Grid with Pagination]
    H --> I[Show Description & Symbol]
    I --> F
    
    %% Add New Budget Code
    F -->|Add New| J[Show Add Form]
    J --> K[User Enters Description & Symbol]
    K --> L{Validation Check}
    L -->|Empty Fields| M[Show Required Field Error]
    M --> K
    L -->|Valid Data| N[Check Symbol Uniqueness]
    N --> O{Symbol Exists?}
    O -->|Yes| P[Show 'Budget Code Already Exists' Alert]
    P --> K
    O -->|No| Q[Convert Symbol to Uppercase]
    Q --> R[Insert into tblMIS_BudgetCode]
    R --> S{Insert Success?}
    S -->|Success| T[Show 'Record Inserted' Message]
    S -->|Error| U[Show Error Message]
    T --> V[Refresh Grid Display]
    U --> V
    V --> F
    
    %% Edit Budget Code
    F -->|Edit Record| W[Enable Edit Mode for Row]
    W --> X[User Modifies Description]
    X --> Y{Update Validation}
    Y -->|Invalid| Z[Show Validation Error]
    Z --> X
    Y -->|Valid| AA[Confirm Update Action]
    AA --> BB{User Confirms?}
    BB -->|No| CC[Cancel Edit Mode]
    CC --> F
    BB -->|Yes| DD[Update tblMIS_BudgetCode Record]
    DD --> EE{Update Success?}
    EE -->|Success| FF[Show 'Record Updated' Message]
    EE -->|Error| GG[Show Update Error]
    FF --> HH[Refresh Grid Display]
    GG --> HH
    HH --> F
    
    %% Delete Budget Code
    F -->|Delete Record| II[Show Delete Confirmation]
    II --> JJ{User Confirms Delete?}
    JJ -->|No| F
    JJ -->|Yes| KK[Delete from tblMIS_BudgetCode]
    KK --> LL{Delete Success?}
    LL -->|Success| MM[Show 'Record Deleted' Message]
    LL -->|Error| NN[Show Delete Error]
    MM --> OO[Refresh Grid Display]
    NN --> OO
    OO --> F
    
    %% Navigation
    F -->|Cancel/Back| PP[Navigate to MIS Transactions Menu]
    F -->|Pagination| QQ[Load Next/Previous Page]
    QQ --> G
    
    %% Django Implementation Flow
    F -->|Django Path| RR[Django BudgetCode Model]
    RR --> SS[BudgetCodeForm Validation]
    SS --> TT[BudgetCodeListView/CreateView/UpdateView]
    TT --> UU[Template Rendering with SAP Fiori UI]
    UU --> VV[HTMX Dynamic Updates]
    VV --> WW[Company Context Integration]
    WW --> F
    
    %% Database Schema
    XX[Database: tblMIS_BudgetCode] --> YY[Fields: Id, Description, Symbol]
    YY --> ZZ[Constraints: Symbol Uniqueness]
    ZZ --> G
    
    %% Business Rules
    AAA[Business Rules] --> BBB[Symbol must be unique]
    BBB --> CCC[Symbol converted to uppercase]
    CCC --> DDD[Description is required]
    DDD --> EEE[Maximum 2 characters for Symbol]
    EEE --> L
    
    %% Error Handling
    FFF[Error Handling] --> GGG[Client-side Validation]
    GGG --> HHH[Server-side Validation]
    HHH --> III[Database Constraint Checks]
    III --> JJJ[User-friendly Error Messages]
    JJJ --> L
    
    %% Security & Permissions
    KKK[Security Layer] --> LLL[User Authentication Required]
    LLL --> MMM[MIS Module Access Permission]
    MMM --> NNN[CRUD Operation Authorization]
    NNN --> B
    
    %% Integration Points
    OOO[Integration Points] --> PPP[Budget Allocation References]
    PPP --> QQQ[Department Budget Links]
    QQQ --> RRR[Work Order Budget Connections]
    RRR --> SSS[Financial Year Context]
    SSS --> D
    
    %% Audit Trail
    TTT[Audit Trail] --> UUU[Creation Timestamp]
    UUU --> VVV[Modification Tracking]
    VVV --> WWW[User Action Logging]
    WWW --> XXX[Change History]
    XXX --> R
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style R fill:#e8f5e8
    style DD fill:#e8f5e8
    style KK fill:#ffebee
    style P fill:#fff3e0
    style M fill:#fff3e0
    style RR fill:#f1f8e9
    style XX fill:#fafafa
    style AAA fill:#e3f2fd
    style FFF fill:#fce4ec
    style KKK fill:#f3e5f5
    style OOO fill:#e0f2f1
    style TTT fill:#fff8e1
