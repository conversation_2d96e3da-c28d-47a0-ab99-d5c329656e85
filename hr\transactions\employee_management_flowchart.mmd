flowchart TD
    A[User Access Employee Management] --> B{Authentication & Session Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Initialize Session Variables]
    D --> E[CompId, SessionId, FinYearId]
    E --> F[Load Employee Management Dashboard]
    
    F --> G{Employee Management Action}
    
    %% New Employee Registration
    G -->|New Employee| H[Load Offer Letters Grid]
    H --> I[Query tblHR_Offer_Master]
    I --> J[Display Available Offers]
    J --> K[Show OfferId, Employee Name, Staff Type]
    K --> L{User Selects Offer}
    
    L -->|Select Offer| M[Navigate to Employee Details Form]
    M --> N[Load OfficeStaff_New_Details.aspx]
    N --> O[Pre-populate from Offer Data]
    O --> P[Employee Registration Form]
    
    P --> Q[Personal Information Section]
    Q --> R[Employee Name, Address, Contact]
    R --> S[Email, Date of Birth, Gender]
    S --> T[Emergency Contact Details]
    
    P --> U[Employment Information Section]
    U --> V[Employee ID Generation]
    V --> W[Department Selection]
    W --> X[Designation Assignment]
    X --> Y[Grade Assignment]
    Y --> Z[Staff Type Selection]
    
    P --> AA[Salary & Benefits Section]
    AA --> BB[Basic Salary Configuration]
    BB --> CC[Allowances Setup]
    CC --> DD[Deductions Configuration]
    DD --> EE[PF & ESI Setup]
    EE --> FF[Overtime Configuration]
    
    P --> GG[Additional Information]
    GG --> HH[Duty Hours Assignment]
    HH --> II[Reporting Manager]
    II --> JJ[Joining Date]
    JJ --> KK[Probation Period]
    
    KK --> LL{Form Validation}
    LL -->|Invalid| MM[Show Validation Errors]
    MM --> P
    LL -->|Valid| NN[Save Employee Record]
    NN --> OO[Insert into tblHR_OfficeStaff]
    OO --> PP[Generate Employee ID]
    PP --> QQ[Create User Account]
    QQ --> RR[Setup Initial Permissions]
    RR --> SS[Send Welcome Email]
    SS --> TT[Update Offer Status]
    TT --> UU[Employee Registration Complete]
    
    %% Employee Profile Management
    G -->|Manage Employees| VV[Load Employee List]
    VV --> WW[Query tblHR_OfficeStaff]
    WW --> XX[Display Employee Grid]
    XX --> YY[Show Employee Details]
    YY --> ZZ{Employee Action}
    
    ZZ -->|View Profile| AAA[Load Employee Profile]
    AAA --> BBB[Display Complete Employee Information]
    BBB --> CCC[Personal, Employment, Salary Details]
    
    ZZ -->|Edit Profile| DDD[Load Edit Form]
    DDD --> EEE[Enable Profile Editing]
    EEE --> FFF[Update Employee Information]
    FFF --> GGG{Update Validation}
    GGG -->|Invalid| HHH[Show Update Errors]
    HHH --> EEE
    GGG -->|Valid| III[Update tblHR_OfficeStaff]
    III --> JJJ[Log Profile Changes]
    JJJ --> KKK[Send Update Notification]
    KKK --> LLL[Profile Update Complete]
    
    ZZ -->|Delete Employee| MMM[Show Delete Confirmation]
    MMM --> NNN{Confirm Deletion?}
    NNN -->|No| ZZ
    NNN -->|Yes| OOO[Check Employee Dependencies]
    OOO --> PPP{Has Dependencies?}
    PPP -->|Yes| QQQ[Show Cannot Delete Error]
    QQQ --> ZZ
    PPP -->|No| RRR[Soft Delete Employee]
    RRR --> SSS[Update Status to Inactive]
    SSS --> TTT[Archive Employee Data]
    TTT --> UUU[Employee Deletion Complete]
    
    %% Employee Lifecycle Management
    G -->|Employee Lifecycle| VVV[Lifecycle Management Options]
    VVV --> WWW{Lifecycle Action}
    
    WWW -->|Promotion| XXX[Load Promotion Form]
    XXX --> YYY[Select Employee]
    YYY --> ZZZ[New Designation/Grade]
    ZZZ --> AAAA[Salary Revision]
    AAAA --> BBBB[Effective Date]
    BBBB --> CCCC[Process Promotion]
    CCCC --> DDDD[Update Employee Record]
    DDDD --> EEEE[Generate Promotion Letter]
    
    WWW -->|Transfer| FFFF[Load Transfer Form]
    FFFF --> GGGG[Select Employee]
    GGGG --> HHHH[New Department/Location]
    HHHH --> IIII[Transfer Date]
    IIII --> JJJJ[Process Transfer]
    JJJJ --> KKKK[Update Department Assignment]
    KKKK --> LLLL[Generate Transfer Order]
    
    WWW -->|Resignation| MMMM[Load Resignation Form]
    MMMM --> NNNN[Select Employee]
    NNNN --> OOOO[Resignation Date]
    OOOO --> PPPP[Notice Period]
    PPPP --> QQQQ[Exit Interview]
    QQQQ --> RRRR[Final Settlement]
    RRRR --> SSSS[Process Resignation]
    SSSS --> TTTT[Update Employee Status]
    TTTT --> UUUU[Generate Relieving Letter]
    
    %% Employee Search and Filter
    G -->|Search Employees| VVVV[Employee Search Interface]
    VVVV --> WWWW[Search Criteria]
    WWWW --> XXXX[Employee ID/Name Search]
    XXXX --> YYYY[Department Filter]
    YYYY --> ZZZZ[Designation Filter]
    ZZZZ --> AAAAA[Status Filter]
    AAAAA --> BBBBB[Date Range Filter]
    BBBBB --> CCCCC[Execute Search]
    CCCCC --> DDDDD[Display Search Results]
    DDDDD --> EEEEE[Export Search Results]
    
    %% Django Implementation
    F --> FFFFF[Django HR Employee Views]
    FFFFF --> GGGGG[Employee Model Integration]
    GGGGG --> HHHHH[EmployeeForm Validation]
    HHHHH --> IIIII[Employee CRUD Views]
    IIIII --> JJJJJ[Profile Management Views]
    JJJJJ --> KKKKK[Lifecycle Management Views]
    KKKKK --> LLLLL[Employee Search Views]
    LLLLL --> MMMMM[SAP Fiori UI Templates]
    MMMMM --> NNNNN[HTMX Dynamic Updates]
    NNNNN --> OOOOO[Company Context Integration]
    
    %% Database Schema
    PPPPP[Database Schema] --> QQQQQ[tblHR_OfficeStaff]
    QQQQQ --> RRRRR[tblHR_Offer_Master]
    RRRRR --> SSSSS[tblHR_Departments]
    SSSSS --> TTTTT[tblHR_Designation]
    TTTTT --> UUUUU[tblHR_Grade]
    UUUUU --> VVVVV[Employee Relationships]
    
    %% Business Rules
    WWWWW[Business Rules] --> XXXXX[Unique Employee ID]
    XXXXX --> YYYYY[Valid Department Assignment]
    YYYYY --> ZZZZZ[Salary Range Validation]
    ZZZZZ --> AAAAAA[Probation Period Rules]
    AAAAAA --> BBBBBB[Reporting Hierarchy]
    BBBBBB --> CCCCCC[Employment Status Rules]
    
    %% Integration Points
    DDDDDD[Integration Points] --> EEEEEE[Payroll System]
    EEEEEE --> FFFFFF[Attendance System]
    FFFFFF --> GGGGGG[Leave Management]
    GGGGGG --> HHHHHH[Performance Management]
    HHHHHH --> IIIIII[Asset Management]
    IIIIII --> JJJJJJ[Communication Systems]
    
    %% Security & Permissions
    KKKKKK[Security Layer] --> LLLLLL[Employee Data Privacy]
    LLLLLL --> MMMMMM[Role-based Access]
    MMMMMM --> NNNNNN[Profile Edit Permissions]
    NNNNNN --> OOOOOO[Sensitive Data Protection]
    OOOOOO --> PPPPPP[Audit Trail Logging]
    
    %% Error Handling
    QQQQQQ[Error Handling] --> RRRRRR[Form Validation]
    RRRRRR --> SSSSSS[Database Constraints]
    SSSSSS --> TTTTTT[Business Rule Validation]
    TTTTTT --> UUUUUU[User Feedback]
    UUUUUU --> VVVVVV[Exception Management]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#e8f5e8
    style VV fill:#e8f5e8
    style VVV fill:#e8f5e8
    style VVVV fill:#e8f5e8
    style NN fill:#e8f5e8
    style III fill:#e8f5e8
    style CCCC fill:#e8f5e8
    style JJJJ fill:#e8f5e8
    style SSSS fill:#e8f5e8
    style MM fill:#fff3e0
    style HHH fill:#fff3e0
    style QQQ fill:#fff3e0
    style FFFFF fill:#f1f8e9
    style PPPPP fill:#fafafa
    style WWWWW fill:#e3f2fd
    style DDDDDD fill:#e0f2f1
    style KKKKKK fill:#f3e5f5
    style QQQQQQ fill:#fce4ec
