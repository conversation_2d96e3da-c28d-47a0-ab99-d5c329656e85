graph TD
    A[Schedule Output - Job Completion] --> B[Work Order Selection]
    A --> C[Output Recording]
    A --> D[Completion Management]

    %% Work Order Selection for Output
    B --> B1[Load Available Work Orders]
    B1 --> B2[Apply Company Filter]
    B2 --> B3[Get Work Orders with Schedules]
    B3 --> B4[Enhance with Customer Info]
    B4 --> B5[Display Work Order Dropdown]
    B5 --> B6[Select Work Order]
    B6 --> B7{Work Order Selected?}
    B7 -->|Yes| B8[Load Work Order Details]
    B7 -->|No| B9[Stay on Selection Page]
    
    B8 --> B10[Get Customer Information]
    B10 --> B11[Load BOM Items for WO]
    B11 --> B12[Filter Items with Schedules]
    B12 --> B13[Display Item List]
    B13 --> B14[Select Item for Output]
    B14 --> B15{Item Selected?}
    B15 -->|Yes| B16[Navigate to Output Details]
    B15 -->|No| B17[Stay on Item Selection]
    
    B16 --> C1[Output Recording Details]

    %% Output Recording Flow
    C1 --> C2[Load Item Information]
    C2 --> C3[Load Scheduled Jobs]
    C3 --> C4[Filter Unreleased Jobs]
    C4 --> C5[Display Scheduled Jobs List]
    C5 --> C6{Scheduled Jobs Found?}
    C6 -->|No| C7[No Scheduled Jobs Message]
    C6 -->|Yes| C8[Job Selection for Output]
    
    C8 --> C9[Display Job Details]
    C9 --> C10[Show Machine Information]
    C10 --> C11[Show Process Details]
    C11 --> C12[Show Scheduled Quantity]
    C12 --> C13[Show Operator Assignment]
    C13 --> C14[Show Time Schedule]
    C14 --> C15[Select Job for Output]
    C15 --> C16{Job Selected?}
    C16 -->|Yes| C17[Output Entry Form]
    C16 -->|No| C18[Stay on Job Selection]
    
    C17 --> C19[Output Quantity Entry]
    C19 --> C20[Enter Produced Quantity]
    C20 --> C21[UOM Verification]
    C21 --> C22[Quality Check Entry]
    C22 --> C23[Quality Status Selection]
    C23 --> C24{Quality Status}
    C24 -->|Pass| C25[Accept Quantity]
    C24 -->|Fail| C26[Reject Quantity]
    C24 -->|Rework| C27[Rework Quantity]
    
    C25 --> C28[Completion Status Check]
    C26 --> C29[Record Rejection Reason]
    C27 --> C30[Record Rework Details]
    
    C29 --> C28
    C30 --> C28
    
    C28 --> C31{Job Completion Status}
    C31 -->|Complete| C32[Mark Job as Complete]
    C31 -->|Partial| C33[Update Job Progress]
    
    C32 --> C34[Validate Complete Quantity]
    C33 --> C35[Calculate Remaining Quantity]
    
    C34 --> C36{Quantity Valid?}
    C36 -->|No| C37[Show Quantity Error]
    C36 -->|Yes| C38[Record Job Completion]
    
    C35 --> C39[Update Schedule Status]
    C37 --> C19
    
    C38 --> C40[Create Job Completion Record]
    C40 --> C41[Set Output Quantity]
    C41 --> C42[Set UOM]
    C42 --> C43[Link to Schedule Detail]
    C43 --> C44[Save Completion Record]
    
    C39 --> C45[Update Progress Percentage]
    C45 --> C46[Calculate Completion Ratio]
    C46 --> C47[Update Schedule Detail]
    C47 --> C48[Save Progress Update]
    
    C44 --> C49[Update Inventory]
    C48 --> C49
    
    C49 --> C50[Check Inventory Integration]
    C50 --> C51{Inventory Update Required?}
    C51 -->|Yes| C52[Update Stock Levels]
    C51 -->|No| C53[Skip Inventory Update]
    
    C52 --> C54[Add to Finished Goods]
    C54 --> C55[Update Item Stock]
    C55 --> C56[Record Stock Movement]
    C56 --> C57[Generate Stock Transaction]
    C57 --> C58[Update Inventory Tables]
    
    C53 --> C59[Generate Output Report]
    C58 --> C59
    
    C59 --> C60[Create Output Summary]
    C60 --> C61[Include Job Details]
    C61 --> C62[Include Quantity Information]
    C62 --> C63[Include Quality Status]
    C63 --> C64[Include Time Information]
    C64 --> C65[Generate Report]
    
    C65 --> C66[Update Schedule Status]
    C66 --> C67[Check Overall Job Status]
    C67 --> C68{All Jobs Complete?}
    C68 -->|Yes| C69[Mark Schedule Complete]
    C68 -->|No| C70[Keep Schedule Active]
    
    C69 --> C71[Update Work Order Status]
    C70 --> C72[Success Message]
    C71 --> C72
    C72 --> C73[Redirect to Output List]

    %% Completion Management Flow
    D --> D1[Output History View]
    D1 --> D2[Load Job Completions]
    D2 --> D3[Apply Company Filter]
    D3 --> D4[Group by Work Order]
    D4 --> D5[Group by Item]
    D5 --> D6[Display Completion List]
    
    D6 --> D7[Completion Actions]
    D7 --> D8[View Completion Details]
    D7 --> D9[Edit Completion]
    D7 --> D10[Delete Completion]
    D7 --> D11[Print Completion Report]
    
    %% View Completion Details
    D8 --> D12[Load Completion Record]
    D12 --> D13[Display Job Information]
    D13 --> D14[Show Output Quantity]
    D14 --> D15[Show Quality Status]
    D15 --> D16[Show Completion Time]
    D16 --> D17[Show Operator Details]
    D17 --> D18[Show Machine Information]
    D18 --> D19[Show Inventory Impact]
    
    %% Edit Completion
    D9 --> D20[Load Completion for Edit]
    D20 --> D21[Check Edit Permissions]
    D21 --> D22{Edit Allowed?}
    D22 -->|No| D23[Show Permission Error]
    D22 -->|Yes| D24[Pre-populate Form]
    
    D23 --> D6
    D24 --> D25[Allow Quantity Modification]
    D25 --> D26[Allow Quality Status Change]
    D26 --> D27[Validate Changes]
    D27 --> D28{Changes Valid?}
    D28 -->|No| D29[Show Validation Errors]
    D28 -->|Yes| D30[Save Changes]
    
    D29 --> D25
    D30 --> D31[Update Inventory Impact]
    D31 --> D32[Recalculate Schedule Status]
    D32 --> D33[Update System Date/Time]
    D33 --> D34[Success Message]
    
    %% Delete Completion
    D10 --> D35[Confirm Deletion]
    D35 --> D36{Confirm Delete?}
    D36 -->|No| D6
    D36 -->|Yes| D37[Check Dependencies]
    D37 --> D38{Has Dependencies?}
    D38 -->|Yes| D39[Show Dependency Error]
    D38 -->|No| D40[Delete Completion Record]
    
    D39 --> D6
    D40 --> D41[Reverse Inventory Impact]
    D41 --> D42[Update Schedule Status]
    D42 --> D43[Recalculate Job Progress]
    D43 --> D44[Success Message]
    D44 --> D6
    
    %% Print Completion Report
    D11 --> D45[Generate Completion Report]
    D45 --> D46[Include Job Summary]
    D46 --> D47[Include Output Details]
    D47 --> D48[Include Quality Information]
    D48 --> D49[Include Time Analysis]
    D49 --> D50[Include Operator Performance]
    D50 --> D51[Format Report]
    D51 --> D52[Generate PDF/Print]

    %% Additional Output Tracking
    C --> E1[Output Dashboard]
    E1 --> E2[Today's Output Summary]
    E1 --> E3[Pending Jobs]
    E1 --> E4[Completed Jobs]
    E1 --> E5[Quality Metrics]
    
    E2 --> E6[Calculate Daily Output]
    E6 --> E7[Show Production Metrics]
    E7 --> E8[Display Efficiency Ratios]
    
    E3 --> E9[List Pending Jobs]
    E9 --> E10[Show Overdue Jobs]
    E10 --> E11[Priority Assignment]
    
    E4 --> E12[List Completed Jobs]
    E12 --> E13[Show Completion Times]
    E13 --> E14[Calculate Performance]
    
    E5 --> E15[Quality Pass Rate]
    E15 --> E16[Rejection Analysis]
    E16 --> E17[Rework Statistics]
    E17 --> E18[Quality Trends]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef qualityClass fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    class A startClass
    class B7,B15,C6,C16,C24,C31,C36,C51,C68,D22,D28,D36,D38 decisionClass
    class C7,C37,D23,D29,D39 errorClass
    class C72,D34,D44 successClass
    class C40,C44,C52,C58,D12,D20,D40 dataClass
    class C22,C23,C25,C26,C27,E15,E16,E17 qualityClass
