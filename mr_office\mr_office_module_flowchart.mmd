graph TD
    A[MR Office Module] --> B[Document Management]
    A --> C[File Operations]
    A --> D[Module Integration]
    A --> E[Dashboard & Reports]

    %% Document Management Section
    B --> B1[Document Upload]
    B --> B2[Document List Management]
    B --> B3[Document Search & Filter]
    B --> B4[Document Organization]

    %% Document Upload Flow
    B1 --> B1a[Load Upload Form]
    B1a --> B1b[Module Selection]
    B1b --> B1c[Format Description]
    B1c --> B1d[File Selection]
    B1d --> B1e[File Validation]
    B1e --> B1f{File Valid?}
    B1f -->|No| B1g[Show Validation Error]
    B1f -->|Yes| B1h[Process File Upload]
    
    B1g --> B1d
    B1h --> B1i[Read File Data]
    B1i --> B1j[Extract File Properties]
    B1j --> B1k[Store File Metadata]
    B1k --> B1l[Save to Database]
    B1l --> B1m[Generate System Info]
    B1m --> B1n[Success Message]
    B1n --> B1o[Refresh Document List]

    %% Document List Management
    B2 --> B2a[Load Documents]
    B2a --> B2b[Apply Company Filter]
    B2b --> B2c[Sort by Module]
    B2c --> B2d[Display Document Grid]
    B2d --> B2e[Document Actions]
    
    B2e --> B2f[View Document Details]
    B2e --> B2g[Download Document]
    B2e --> B2h[Delete Document]
    B2e --> B2i[Edit Document Info]
    
    %% View Document Details
    B2f --> B2j[Load Document Record]
    B2j --> B2k[Display Metadata]
    B2k --> B2l[Show Module Name]
    B2l --> B2m[Show File Properties]
    B2m --> B2n[Show Upload Info]
    B2n --> B2o[Show System Details]
    
    %% Download Document
    B2g --> B2p[Validate Access Rights]
    B2p --> B2q{Access Allowed?}
    B2q -->|No| B2r[Access Denied Error]
    B2q -->|Yes| B2s[Retrieve File Data]
    B2s --> B2t[Set Content Headers]
    B2t --> B2u[Stream File to Browser]
    B2u --> B2v[Log Download Activity]
    
    %% Delete Document
    B2h --> B2w[Confirm Deletion]
    B2w --> B2x{Confirm Delete?}
    B2x -->|No| B2d
    B2x -->|Yes| B2y[Check Dependencies]
    B2y --> B2z{Has Dependencies?}
    B2z -->|Yes| B2aa[Show Dependency Error]
    B2z -->|No| B2bb[Delete Document Record]
    B2bb --> B2cc[Success Message]
    B2cc --> B2d

    %% Document Search & Filter
    B3 --> B3a[Search Interface]
    B3a --> B3b[Module Filter]
    B3a --> B3c[Format Search]
    B3a --> B3d[Date Range Filter]
    B3a --> B3e[File Type Filter]
    
    B3b --> B3f[Filter by Module]
    B3c --> B3g[Search by Format]
    B3d --> B3h[Filter by Date]
    B3e --> B3i[Filter by File Type]
    
    B3f --> B3j[Apply Filters]
    B3g --> B3j
    B3h --> B3j
    B3i --> B3j
    B3j --> B3k[Execute Search Query]
    B3k --> B3l[Display Filtered Results]
    B3l --> B3m[Pagination]

    %% Document Organization
    B4 --> B4a[Module-wise Organization]
    B4a --> B4b[Category Management]
    B4b --> B4c[Format Standardization]
    B4c --> B4d[Version Control]
    B4d --> B4e[Archive Management]

    %% File Operations Section
    C --> C1[File Upload Processing]
    C --> C2[File Download Management]
    C --> C3[File Validation]
    C --> C4[File Storage]

    %% File Upload Processing
    C1 --> C1a[Receive File Upload]
    C1a --> C1b[Validate File Size]
    C1b --> C1c{Size Valid?}
    C1c -->|No| C1d[Size Limit Error]
    C1c -->|Yes| C1e[Validate File Type]
    C1e --> C1f{Type Valid?}
    C1f -->|No| C1g[File Type Error]
    C1f -->|Yes| C1h[Scan for Viruses]
    C1h --> C1i{Virus Free?}
    C1i -->|No| C1j[Security Threat Error]
    C1i -->|Yes| C1k[Process File Content]
    
    C1k --> C1l[Extract Metadata]
    C1l --> C1m[Generate Unique ID]
    C1m --> C1n[Convert to Binary]
    C1n --> C1o[Compress if Needed]
    C1o --> C1p[Store in Database]
    C1p --> C1q[Update File Registry]

    %% File Download Management
    C2 --> C2a[Validate Download Request]
    C2a --> C2b[Check User Permissions]
    C2b --> C2c{Permission Valid?}
    C2c -->|No| C2d[Permission Denied]
    C2c -->|Yes| C2e[Retrieve File Data]
    C2e --> C2f[Decompress if Needed]
    C2f --> C2g[Set MIME Type]
    C2g --> C2h[Set Download Headers]
    C2h --> C2i[Stream to Client]
    C2i --> C2j[Log Download Event]

    %% File Validation
    C3 --> C3a[Size Validation]
    C3a --> C3b[Type Validation]
    C3b --> C3c[Content Validation]
    C3c --> C3d[Security Validation]
    C3d --> C3e[Integrity Check]

    %% File Storage
    C4 --> C4a[Database Storage]
    C4a --> C4b[Binary Data Management]
    C4b --> C4c[Metadata Storage]
    C4c --> C4d[Index Management]
    C4d --> C4e[Backup Strategy]

    %% Module Integration Section
    D --> D1[ERP Module Linking]
    D --> D2[Cross-Module Access]
    D --> D3[Document Referencing]
    D --> D4[Workflow Integration]

    %% ERP Module Linking
    D1 --> D1a[Module Master Integration]
    D1a --> D1b[Dynamic Module Loading]
    D1b --> D1c[Module-specific Documents]
    D1c --> D1d[Access Control by Module]
    D1d --> D1e[Module-based Organization]

    %% Cross-Module Access
    D2 --> D2a[Shared Document Access]
    D2a --> D2b[Inter-module References]
    D2b --> D2c[Document Linking]
    D2c --> D2d[Cross-reference Tracking]

    %% Document Referencing
    D3 --> D3a[Document ID System]
    D3a --> D3b[Reference Management]
    D3b --> D3c[Link Validation]
    D3c --> D3d[Broken Link Detection]

    %% Workflow Integration
    D4 --> D4a[Approval Workflows]
    D4a --> D4b[Document Routing]
    D4b --> D4c[Status Tracking]
    D4c --> D4d[Notification System]

    %% Dashboard & Reports Section
    E --> E1[Document Statistics]
    E --> E2[Usage Analytics]
    E --> E3[Storage Reports]
    E --> E4[Activity Monitoring]

    %% Document Statistics
    E1 --> E1a[Total Documents Count]
    E1a --> E1b[Documents by Module]
    E1b --> E1c[Documents by Type]
    E1c --> E1d[Upload Trends]
    E1d --> E1e[Size Distribution]

    %% Usage Analytics
    E2 --> E2a[Download Statistics]
    E2a --> E2b[Popular Documents]
    E2b --> E2c[User Activity]
    E2c --> E2d[Access Patterns]
    E2d --> E2e[Usage Trends]

    %% Storage Reports
    E3 --> E3a[Storage Utilization]
    E3a --> E3b[Storage by Module]
    E3b --> E3c[File Size Analysis]
    E3c --> E3d[Growth Projections]
    E3d --> E3e[Cleanup Recommendations]

    %% Activity Monitoring
    E4 --> E4a[Upload Activity]
    E4a --> E4b[Download Activity]
    E4b --> E4c[User Actions]
    E4c --> E4d[System Events]
    E4d --> E4e[Audit Trail]

    %% Real-time Features
    A --> F[Real-time Features]
    F --> F1[Live Upload Progress]
    F --> F2[Instant Search]
    F --> F3[Auto-refresh Lists]
    F --> F4[Notifications]

    %% Security Features
    A --> G[Security Features]
    G --> G1[Access Control]
    G --> G2[File Encryption]
    G --> G3[Virus Scanning]
    G --> G4[Audit Logging]

    %% Mobile Support
    A --> H[Mobile Support]
    H --> H1[Responsive Design]
    H --> H2[Touch Interface]
    H --> H3[Mobile Upload]
    H --> H4[Offline Capability]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef documentClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef fileClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef integrationClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef reportClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef securityClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A startClass
    class B,B1,B2,B3,B4 documentClass
    class C,C1,C2,C3,C4 fileClass
    class D,D1,D2,D3,D4 integrationClass
    class E,E1,E2,E3,E4 reportClass
    class B1f,B2q,B2x,B2z,C1c,C1f,C1i,C2c decisionClass
    class B1g,B2r,B2aa,C1d,C1g,C1j,C2d errorClass
    class B1n,B2cc successClass
    class G,G1,G2,G3,G4 securityClass
