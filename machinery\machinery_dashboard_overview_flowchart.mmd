graph TD
    A[Machinery Dashboard] --> B[Machine Status Overview]
    A --> C[Maintenance Alerts]
    A --> D[Production Schedule Summary]
    A --> E[Key Performance Metrics]
    A --> F[Quick Actions]

    %% Machine Status Overview
    B --> B1[Load All Machines]
    B1 --> B2[Apply Company Filter]
    B2 --> B3[Calculate Machine Status]
    B3 --> B4[Active Machines Count]
    B3 --> B5[Idle Machines Count]
    B3 --> B6[Under Maintenance Count]
    B3 --> B7[Out of Service Count]
    
    B4 --> B8[Show Active Machine List]
    B5 --> B9[Show Idle Machine List]
    B6 --> B10[Show Maintenance Machine List]
    B7 --> B11[Show Out of Service List]
    
    B8 --> B12[Machine Status Cards]
    B9 --> B12
    B10 --> B12
    B11 --> B12
    
    B12 --> B13[Status Color Coding]
    B13 --> B14[Green for Active]
    B13 --> B15[Yellow for Idle]
    B13 --> B16[Orange for Maintenance]
    B13 --> B17[Red for Out of Service]
    
    B14 --> B18[Display Status Dashboard]
    B15 --> B18
    B16 --> B18
    B17 --> B18

    %% Maintenance Alerts
    C --> C1[Load Maintenance Data]
    C1 --> C2[Calculate PM Due Dates]
    C2 --> C3[Check Current Date]
    C3 --> C4[Identify Alert Categories]
    
    C4 --> C5[Overdue Maintenance]
    C4 --> C6[Due This Week]
    C4 --> C7[Due Next Week]
    C4 --> C8[Insurance Expiry Alerts]
    C4 --> C9[Warranty Expiry Alerts]
    
    %% Overdue Maintenance
    C5 --> C5a[Calculate Days Overdue]
    C5a --> C5b[Priority Assignment]
    C5b --> C5c{Priority Level}
    C5c -->|Critical| C5d[Red Alert - >30 Days]
    C5c -->|High| C5e[Orange Alert - 15-30 Days]
    C5c -->|Medium| C5f[Yellow Alert - 1-15 Days]
    
    C5d --> C5g[Generate Critical Alert]
    C5e --> C5h[Generate High Alert]
    C5f --> C5i[Generate Medium Alert]
    
    C5g --> C5j[Alert Notification System]
    C5h --> C5j
    C5i --> C5j
    
    %% Due This Week
    C6 --> C6a[Filter Machines Due in 7 Days]
    C6a --> C6b[Calculate Exact Due Dates]
    C6b --> C6c[Sort by Due Date]
    C6c --> C6d[Generate Due Soon List]
    C6d --> C6e[Send Reminder Notifications]
    
    %% Insurance & Warranty Alerts
    C8 --> C8a[Check Insurance Expiry Dates]
    C8a --> C8b[Calculate Days to Expiry]
    C8b --> C8c{Insurance Status}
    C8c -->|Expired| C8d[Red Alert]
    C8c -->|Expiring in 30 Days| C8e[Orange Alert]
    C8c -->|Expiring in 60 Days| C8f[Yellow Alert]
    
    C9 --> C9a[Check Warranty Expiry Dates]
    C9a --> C9b[Calculate Days to Expiry]
    C9b --> C9c{Warranty Status}
    C9c -->|Expired| C9d[Red Alert]
    C9c -->|Expiring in 30 Days| C9e[Orange Alert]
    C9c -->|Expiring in 60 Days| C9f[Yellow Alert]
    
    C8d --> C10[Consolidated Alert Panel]
    C8e --> C10
    C8f --> C10
    C9d --> C10
    C9e --> C10
    C9f --> C10

    %% Production Schedule Summary
    D --> D1[Load Today's Schedule]
    D1 --> D2[Load This Week's Schedule]
    D2 --> D3[Calculate Schedule Metrics]
    D3 --> D4[Jobs Scheduled Today]
    D3 --> D5[Jobs in Progress]
    D3 --> D6[Jobs Completed Today]
    D3 --> D7[Jobs Delayed]
    D3 --> D8[Jobs Pending]
    
    D4 --> D9[Today's Schedule Count]
    D5 --> D10[In Progress Count]
    D6 --> D11[Completed Count]
    D7 --> D12[Delayed Count]
    D8 --> D13[Pending Count]
    
    D9 --> D14[Schedule Summary Cards]
    D10 --> D14
    D11 --> D14
    D12 --> D14
    D13 --> D14
    
    D14 --> D15[Schedule Progress Bar]
    D15 --> D16[Calculate Completion %]
    D16 --> D17[Visual Progress Indicator]
    D17 --> D18[Schedule Status Colors]
    
    D18 --> D19[Machine Loading Chart]
    D19 --> D20[Show Machine Utilization]
    D20 --> D21[Identify Bottlenecks]
    D21 --> D22[Highlight Overloaded Machines]

    %% Key Performance Metrics
    E --> E1[Overall Equipment Effectiveness]
    E --> E2[Machine Utilization Rate]
    E --> E3[Maintenance Cost Metrics]
    E --> E4[Production Efficiency]
    E --> E5[Quality Metrics]
    
    %% OEE Calculation
    E1 --> E1a[Calculate Availability]
    E1a --> E1b[Calculate Performance Rate]
    E1b --> E1c[Calculate Quality Rate]
    E1c --> E1d[Calculate OEE = A × P × Q]
    E1d --> E1e[Display OEE Gauge]
    E1e --> E1f[OEE Trend Chart]
    
    %% Utilization Rate
    E2 --> E2a[Calculate Running Hours]
    E2a --> E2b[Calculate Available Hours]
    E2b --> E2c[Utilization = Running/Available]
    E2c --> E2d[Display Utilization %]
    E2d --> E2e[Utilization by Machine]
    E2e --> E2f[Utilization Trend]
    
    %% Maintenance Cost
    E3 --> E3a[Calculate Monthly Maintenance Cost]
    E3a --> E3b[Calculate Cost per Machine]
    E3b --> E3c[Compare with Budget]
    E3c --> E3d[Cost Variance Analysis]
    E3d --> E3e[Display Cost Metrics]
    
    %% Production Efficiency
    E4 --> E4a[Calculate Planned vs Actual Output]
    E4a --> E4b[Calculate Efficiency %]
    E4b --> E4c[Efficiency by Machine]
    E4c --> E4d[Efficiency Trends]
    E4d --> E4e[Display Efficiency Dashboard]
    
    %% Quality Metrics
    E5 --> E5a[Calculate Pass Rate]
    E5a --> E5b[Calculate Rejection Rate]
    E5b --> E5c[Calculate Rework Rate]
    E5c --> E5d[Quality Trend Analysis]
    E5d --> E5e[Display Quality Dashboard]

    %% Quick Actions
    F --> F1[Add New Machine]
    F --> F2[Schedule Maintenance]
    F --> F3[Create Job Schedule]
    F --> F4[Record Output]
    F --> F5[View Reports]
    F --> F6[Machine Search]
    
    F1 --> F1a[Navigate to Machine Registration]
    F1a --> F1b[Item Selection Page]
    
    F2 --> F2a[Navigate to PMBM]
    F2a --> F2b[Machine Selection for Maintenance]
    
    F3 --> F3a[Navigate to Schedule Input]
    F3a --> F3b[Work Order Selection]
    
    F4 --> F4a[Navigate to Schedule Output]
    F4a --> F4b[Output Recording Page]
    
    F5 --> F5a[Navigate to Reports Menu]
    F5a --> F5b[Report Selection Page]
    
    F6 --> F6a[Open Machine Search]
    F6a --> F6b[Advanced Search Form]

    %% Real-time Updates
    A --> G[Real-time Data Updates]
    G --> G1[Auto-refresh Dashboard]
    G1 --> G2[Update Every 5 Minutes]
    G2 --> G3[Refresh Machine Status]
    G3 --> G4[Refresh Alerts]
    G4 --> G5[Refresh Schedule Data]
    G5 --> G6[Refresh Metrics]
    G6 --> G7[Update Notifications]
    
    G7 --> G8[WebSocket Connections]
    G8 --> G9[Push Notifications]
    G9 --> G10[Browser Notifications]
    G10 --> G11[Email Alerts]
    G11 --> G12[SMS Alerts]

    %% Dashboard Customization
    A --> H[Dashboard Customization]
    H --> H1[Widget Configuration]
    H1 --> H2[Show/Hide Widgets]
    H2 --> H3[Resize Widgets]
    H3 --> H4[Rearrange Layout]
    H4 --> H5[Save User Preferences]
    
    H5 --> H6[Role-based Dashboards]
    H6 --> H7[Manager Dashboard]
    H6 --> H8[Operator Dashboard]
    H6 --> H9[Maintenance Dashboard]
    H6 --> H10[Executive Dashboard]

    %% Mobile Responsiveness
    A --> I[Mobile Dashboard]
    I --> I1[Responsive Design]
    I1 --> I2[Touch-friendly Interface]
    I2 --> I3[Swipe Navigation]
    I3 --> I4[Mobile Alerts]
    I4 --> I5[Offline Capability]
    I5 --> I6[Sync When Online]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef statusClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef alertClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef scheduleClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef metricsClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef actionClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef criticalClass fill:#ffcdd2,stroke:#c62828,stroke-width:3px
    classDef warningClass fill:#fff8e1,stroke:#f9a825,stroke-width:2px

    class A startClass
    class B,B1,B2,B3,B4,B5,B6,B7,B12,B18 statusClass
    class C,C1,C5,C8,C9,C10 alertClass
    class D,D1,D2,D3,D14,D19 scheduleClass
    class E,E1,E2,E3,E4,E5 metricsClass
    class F,F1,F2,F3,F4,F5,F6 actionClass
    class C5c,C8c,C9c decisionClass
    class C5d,C8d,C9d criticalClass
    class C5e,C5f,C8e,C8f,C9e,C9f warningClass
