flowchart TD
    A[User Access Gate Pass Management] --> B{Authentication & Session Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Initialize Session Variables]
    D --> E[CompId, SessionId, FinYearId]
    E --> F[Load Gate Pass Management Dashboard]
    
    F --> G{Gate Pass Management Action}
    
    %% New Gate Pass Request
    G -->|New Gate Pass| H[Load Gate Pass Request Form]
    H --> I[Generate Gate Pass Number]
    I --> J[GPNo = Auto-generated Sequence]
    J --> K[Employee Information Section]
    K --> L[Employee ID Input]
    L --> M[Employee Name Auto-populate]
    M --> N[Department Auto-populate]
    
    K --> O[Gate Pass Details Section]
    O --> P[From Date Selection]
    P --> Q[From Time Selection]
    Q --> R[To Time Selection]
    R --> S[Gate Pass Type Selection]
    S --> T{Gate Pass Type}
    
    T -->|Personal| U[Personal Gate Pass Details]
    T -->|Official| V[Official Gate Pass Details]
    T -->|Emergency| W[Emergency Gate Pass Details]
    
    U --> X[Personal Reason Selection]
    V --> Y[Official Purpose Details]
    W --> Z[Emergency Reason Details]
    
    X --> AA[Place/Destination]
    Y --> AA
    Z --> AA
    AA --> BB[Contact Person Details]
    BB --> CC[Contact Number]
    CC --> DD[Additional Reason/Comments]
    
    DD --> EE[Gate Pass Validation]
    EE --> FF{Validation Check}
    FF -->|Invalid| GG[Show Validation Errors]
    GG --> H
    FF -->|Valid| HH[Save Gate Pass Request]
    HH --> II[Insert into tblGate_Pass]
    II --> JJ[Insert into tblGatePass_Details]
    JJ --> KK[Set Initial Status: Pending]
    KK --> LL[Generate Gate Pass Document]
    LL --> MM[Send for Authorization]
    MM --> NN[Notify Approver]
    NN --> OO[Gate Pass Request Submitted]
    
    %% Gate Pass Authorization
    G -->|Authorize Gate Pass| PP[Load Authorization Interface]
    PP --> QQ[Query Pending Gate Passes]
    QQ --> RR[Display Pending Requests]
    RR --> SS[Show Gate Pass Details]
    SS --> TT[Employee, Date, Time, Reason, Type]
    TT --> UU{Select Gate Pass for Authorization}
    
    UU -->|Authorize| VV[Load Authorization Form]
    VV --> WW[Review Gate Pass Details]
    WW --> XX[Authorizer Information]
    XX --> YY[Authorization Decision]
    YY --> ZZ{Authorization Action}
    
    ZZ -->|Approve| AAA[Approve Gate Pass]
    AAA --> BBB[Update Authorize = 1]
    BBB --> CCC[Set AuthorizedBy]
    CCC --> DDD[Set AuthorizeDate]
    DDD --> EEE[Set AuthorizeTime]
    EEE --> FFF[Generate Approved Gate Pass]
    FFF --> GGG[Send Approval Notification]
    GGG --> HHH[Gate Pass Approved]
    
    ZZ -->|Reject| III[Reject Gate Pass]
    III --> JJJ[Update Authorize = 0]
    JJJ --> KKK[Set Rejection Reason]
    KKK --> LLL[Set AuthorizedBy]
    LLL --> MMM[Set AuthorizeDate/Time]
    MMM --> NNN[Send Rejection Notification]
    NNN --> OOO[Gate Pass Rejected]
    
    ZZ -->|Request Changes| PPP[Request Modifications]
    PPP --> QQQ[Add Modification Comments]
    QQQ --> RRR[Send Back to Employee]
    RRR --> SSS[Notification for Changes]
    SSS --> TTT[Gate Pass Pending Changes]
    
    %% Gate Pass Tracking and Management
    G -->|Track Gate Pass| UUU[Gate Pass Tracking Interface]
    UUU --> VVV[Search Gate Pass Options]
    VVV --> WWW{Search Criteria}
    
    WWW -->|By Employee| XXX[Employee-wise Search]
    WWW -->|By Date Range| YYY[Date Range Search]
    WWW -->|By Status| ZZZ[Status-wise Search]
    WWW -->|By Gate Pass No| AAAA[Gate Pass Number Search]
    
    XXX --> BBBB[Execute Employee Search]
    YYY --> CCCC[Execute Date Search]
    ZZZ --> DDDD[Execute Status Search]
    AAAA --> EEEE[Execute GP Number Search]
    
    BBBB --> FFFF[Display Search Results]
    CCCC --> FFFF
    DDDD --> FFFF
    EEEE --> FFFF
    FFFF --> GGGG[Show Gate Pass List]
    GGGG --> HHHH[Display Status, Dates, Authorization]
    
    %% Gate Pass Printing and Reports
    G -->|Print Gate Pass| IIII[Gate Pass Printing Interface]
    IIII --> JJJJ[Select Gate Pass for Printing]
    JJJJ --> KKKK[Load Gate Pass Print Template]
    KKKK --> LLLL[Format Gate Pass Document]
    LLLL --> MMMM[Include Employee Details]
    MMMM --> NNNN[Include Authorization Details]
    NNNN --> OOOO[Include Company Information]
    OOOO --> PPPP[Generate Printable Gate Pass]
    PPPP --> QQQQ[Print Gate Pass Document]
    
    G -->|Gate Pass Reports| RRRR[Gate Pass Reporting Interface]
    RRRR --> SSSS{Report Type}
    SSSS -->|Daily Report| TTTT[Daily Gate Pass Report]
    SSSS -->|Monthly Report| UUUU[Monthly Gate Pass Report]
    SSSS -->|Employee-wise| VVVV[Employee Gate Pass Report]
    SSSS -->|Department-wise| WWWW[Department Gate Pass Report]
    SSSS -->|Status Report| XXXX[Gate Pass Status Report]
    
    TTTT --> YYYY[Generate Daily Report]
    UUUU --> ZZZZ[Generate Monthly Report]
    VVVV --> AAAAA[Generate Employee Report]
    WWWW --> BBBBB[Generate Department Report]
    XXXX --> CCCCC[Generate Status Report]
    
    %% Gate Pass Editing and Deletion
    G -->|Edit Gate Pass| DDDDD[Gate Pass Edit Interface]
    DDDDD --> EEEEE[Search Gate Pass to Edit]
    EEEEE --> FFFFF[Select Gate Pass]
    FFFFF --> GGGGG{Gate Pass Status Check}
    GGGGG -->|Pending| HHHHH[Allow Edit]
    GGGGG -->|Approved| IIIII[Restrict Edit]
    GGGGG -->|Rejected| JJJJJ[Allow Re-submission]
    
    HHHHH --> KKKKK[Load Edit Form]
    KKKKK --> LLLLL[Modify Gate Pass Details]
    LLLLL --> MMMMM[Update Gate Pass Record]
    MMMMM --> NNNNN[Reset Authorization Status]
    NNNNN --> OOOOO[Send for Re-authorization]
    
    G -->|Delete Gate Pass| PPPPP[Gate Pass Deletion Interface]
    PPPPP --> QQQQQ[Select Gate Pass to Delete]
    QQQQQ --> RRRRR{Deletion Validation}
    RRRRR -->|Can Delete| SSSSS[Confirm Deletion]
    RRRRR -->|Cannot Delete| TTTTT[Show Deletion Error]
    SSSSS --> UUUUU[Delete Gate Pass Records]
    UUUUU --> VVVVV[Log Deletion Activity]
    
    %% Django Implementation
    F --> WWWWW[Django HR Gate Pass Views]
    WWWWW --> XXXXX[GatePass Model Integration]
    XXXXX --> YYYYY[GatePassForm Validation]
    YYYYY --> ZZZZZ[Gate Pass CRUD Views]
    ZZZZZ --> AAAAAA[Authorization Workflow Views]
    AAAAAA --> BBBBBB[Gate Pass Tracking Views]
    BBBBBB --> CCCCCC[Gate Pass Reporting Views]
    CCCCCC --> DDDDDD[SAP Fiori Gate Pass UI]
    DDDDDD --> EEEEEE[HTMX Dynamic Updates]
    EEEEEE --> FFFFFF[Company Context Integration]
    
    %% Database Schema
    GGGGGG[Database Schema] --> HHHHHH[tblGate_Pass]
    HHHHHH --> IIIIII[tblGatePass_Details]
    IIIIII --> JJJJJJ[tblGatePass_Reason]
    JJJJJJ --> KKKKKK[tblHR_OfficeStaff]
    KKKKKK --> LLLLLL[Gate Pass Relationships]
    
    %% Business Rules
    MMMMMM[Business Rules] --> NNNNNN[Gate Pass Time Validation]
    NNNNNN --> OOOOOO[Authorization Hierarchy]
    OOOOOO --> PPPPPP[Gate Pass Duration Limits]
    PPPPPP --> QQQQQQ[Emergency Gate Pass Rules]
    QQQQQQ --> RRRRRR[Official Gate Pass Approval]
    RRRRRR --> SSSSSS[Personal Gate Pass Limits]
    
    %% Workflow Management
    TTTTTT[Workflow Management] --> UUUUUU[Request Submission]
    UUUUUU --> VVVVVV[Authorization Process]
    VVVVVV --> WWWWWW[Approval Notifications]
    WWWWWW --> XXXXXX[Status Tracking]
    XXXXXX --> YYYYYY[Document Generation]
    YYYYYY --> ZZZZZZ[Audit Trail]
    
    %% Integration Points
    AAAAAAA[Integration Points] --> BBBBBBB[Employee Management]
    BBBBBBB --> CCCCCCC[Attendance System]
    CCCCCCC --> DDDDDDD[Security System]
    DDDDDDD --> EEEEEEE[Notification System]
    EEEEEEE --> FFFFFFF[Reporting System]
    FFFFFFF --> GGGGGGG[Document Management]
    
    %% Security & Permissions
    HHHHHHH[Security Layer] --> IIIIIII[Gate Pass Data Security]
    IIIIIII --> JJJJJJJ[Authorization Rights]
    JJJJJJJ --> KKKKKKK[Employee Privacy]
    KKKKKKK --> LLLLLLL[Access Control]
    LLLLLLL --> MMMMMMM[Audit Logging]
    
    %% Error Handling
    NNNNNNN[Error Handling] --> OOOOOOO[Form Validation]
    OOOOOOO --> PPPPPPP[Business Rule Validation]
    PPPPPPP --> QQQQQQQ[Authorization Validation]
    QQQQQQQ --> RRRRRRR[User Feedback]
    RRRRRRR --> SSSSSSS[Exception Management]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#e8f5e8
    style PP fill:#e8f5e8
    style UUU fill:#e8f5e8
    style IIII fill:#e8f5e8
    style RRRR fill:#e8f5e8
    style DDDDD fill:#e8f5e8
    style PPPPP fill:#e8f5e8
    style HH fill:#e8f5e8
    style AAA fill:#e8f5e8
    style III fill:#ffebee
    style GG fill:#fff3e0
    style TTTTT fill:#fff3e0
    style QQQQ fill:#fff3e0
    style YYYY fill:#fff3e0
    style ZZZZ fill:#fff3e0
    style AAAAA fill:#fff3e0
    style BBBBB fill:#fff3e0
    style CCCCC fill:#fff3e0
    style WWWWW fill:#f1f8e9
    style GGGGGG fill:#fafafa
    style MMMMMM fill:#e3f2fd
    style TTTTTT fill:#e8f5e8
    style AAAAAAA fill:#e0f2f1
    style HHHHHHH fill:#f3e5f5
    style NNNNNNN fill:#fce4ec
