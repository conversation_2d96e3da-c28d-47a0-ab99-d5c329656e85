flowchart TD
    A[User Access Staff Management] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Staff Management Dashboard]
    
    D --> E[Display Staff Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Staff| G[Query tblHR_OfficeStaff]
    G --> H[Display Staff Grid with Pagination]
    H --> I[Show EmpId, Name, Dept, Designation]
    I --> J[Show Edit/Delete/View Links]
    J --> F
    
    %% Add New Staff
    F -->|Add New Staff| K[Load Staff Registration Form]
    K --> L[Personal Information Section]
    L --> M[Employee ID Generation]
    M --> N[Full Name Input]
    N --> O[Date of Birth]
    O --> P[Gender Selection]
    P --> Q[Contact Information]
    Q --> R[Address Details]
    R --> S[Emergency Contact]
    S --> T[Employment Details Section]
    T --> U[Department Selection]
    U --> V[Designation Selection]
    V --> W[Grade Assignment]
    W --> X[Business Group Assignment]
    X --> Y[Reporting Manager]
    Y --> Z[Joining Date]
    Z --> AA[Employment Type]
    AA --> BB[Salary Information Section]
    BB --> CC[Basic Salary]
    CC --> DD[Allowances Setup]
    DD --> EE[Benefits Configuration]
    EE --> FF[Bank Details]
    FF --> GG[Document Upload Section]
    GG --> HH[Photo Upload]
    HH --> II[Resume/CV Upload]
    II --> JJ[ID Proof Upload]
    JJ --> KK[Address Proof Upload]
    KK --> LL[Educational Certificates]
    LL --> MM{Validation Check}
    MM -->|Invalid| NN[Show Validation Errors]
    NN --> K
    MM -->|Valid| OO[Insert into tblHR_OfficeStaff]
    OO --> PP[Generate Employee ID]
    PP --> QQ[Create Employee Profile]
    QQ --> RR[Setup Initial Permissions]
    RR --> SS[Send Welcome Email]
    SS --> TT[Staff Registration Complete]
    TT --> F
    
    %% Edit Staff Details
    F -->|Edit Staff| UU[Load Staff Edit Form]
    UU --> VV[Load Existing Staff Data]
    VV --> WW[Enable Form Editing]
    WW --> XX[User Modifies Staff Details]
    XX --> YY{Update Validation}
    YY -->|Invalid| ZZ[Show Validation Errors]
    ZZ --> XX
    YY -->|Valid| AAA[Update tblHR_OfficeStaff]
    AAA --> BBB[Update Related Records]
    BBB --> CCC[Log Changes]
    CCC --> DDD[Send Update Notification]
    DDD --> EEE[Staff Update Complete]
    EEE --> F
    
    %% Staff Profile Management
    F -->|Manage Profile| FFF[Load Staff Profile]
    FFF --> GGG{Profile Action}
    GGG -->|View Profile| HHH[Display Complete Profile]
    GGG -->|Update Photo| III[Update Staff Photo]
    GGG -->|Update Documents| JJJ[Update Staff Documents]
    GGG -->|Update Contact| KKK[Update Contact Information]
    GGG -->|Update Emergency| LLL[Update Emergency Contact]
    
    HHH --> MMM[Show Profile Details]
    III --> NNN[Upload New Photo]
    JJJ --> OOO[Upload New Documents]
    KKK --> PPP[Update Contact Details]
    LLL --> QQQ[Update Emergency Details]
    
    MMM --> F
    NNN --> RRR[Save Photo Update]
    OOO --> SSS[Save Document Update]
    PPP --> TTT[Save Contact Update]
    QQQ --> UUU[Save Emergency Update]
    
    RRR --> F
    SSS --> F
    TTT --> F
    UUU --> F
    
    %% Staff Search and Filter
    F -->|Search Staff| VVV[Load Staff Search Interface]
    VVV --> WWW{Search Criteria}
    WWW -->|By Name| XXX[Search by Employee Name]
    WWW -->|By Department| YYY[Search by Department]
    WWW -->|By Designation| ZZZ[Search by Designation]
    WWW -->|By Employee ID| AAAA[Search by Employee ID]
    WWW -->|By Status| BBBB[Search by Employment Status]
    
    XXX --> CCCC[Execute Name Search]
    YYY --> DDDD[Execute Department Search]
    ZZZ --> EEEE[Execute Designation Search]
    AAAA --> FFFF[Execute ID Search]
    BBBB --> GGGG[Execute Status Search]
    
    CCCC --> HHHH[Display Search Results]
    DDDD --> HHHH
    EEEE --> HHHH
    FFFF --> HHHH
    GGGG --> HHHH
    HHHH --> IIII[Show Filtered Staff List]
    IIII --> F
    
    %% Staff Reports
    F -->|Staff Reports| JJJJ[Staff Reporting Interface]
    JJJJ --> KKKK{Report Type}
    KKKK -->|Employee Directory| LLLL[Employee Directory Report]
    KKKK -->|Department Report| MMMM[Department-wise Staff Report]
    KKKK -->|Designation Report| NNNN[Designation-wise Staff Report]
    KKKK -->|New Joiners| OOOO[New Joiners Report]
    KKKK -->|Staff Summary| PPPP[Staff Summary Report]
    
    LLLL --> QQQQ[Generate Directory Report]
    MMMM --> RRRR[Generate Department Report]
    NNNN --> SSSS[Generate Designation Report]
    OOOO --> TTTT[Generate Joiners Report]
    PPPP --> UUUU[Generate Summary Report]
    
    QQQQ --> VVVV[Export Staff Reports]
    RRRR --> VVVV
    SSSS --> VVVV
    TTTT --> VVVV
    UUUU --> VVVV
    VVVV --> F
    
    %% Staff Status Management
    F -->|Manage Status| WWWW[Staff Status Management]
    WWWW --> XXXX{Status Action}
    XXXX -->|Activate| YYYY[Activate Staff]
    XXXX -->|Deactivate| ZZZZ[Deactivate Staff]
    XXXX -->|Transfer| AAAAA[Transfer Staff]
    XXXX -->|Promote| BBBBB[Promote Staff]
    XXXX -->|Terminate| CCCCC[Terminate Staff]
    
    YYYY --> DDDDD[Update Status to Active]
    ZZZZ --> EEEEE[Update Status to Inactive]
    AAAAA --> FFFFF[Process Staff Transfer]
    BBBBB --> GGGGG[Process Staff Promotion]
    CCCCC --> HHHHH[Process Staff Termination]
    
    DDDDD --> IIIII[Log Status Change]
    EEEEE --> IIIII
    FFFFF --> IIIII
    GGGGG --> IIIII
    HHHHH --> IIIII
    IIIII --> JJJJJ[Send Status Notification]
    JJJJJ --> F
    
    %% Staff Bulk Operations
    F -->|Bulk Operations| KKKKK[Staff Bulk Operations]
    KKKKK --> LLLLL{Bulk Action}
    LLLLL -->|Bulk Import| MMMMM[Bulk Staff Import]
    LLLLL -->|Bulk Update| NNNNN[Bulk Staff Update]
    LLLLL -->|Bulk Export| OOOOO[Bulk Staff Export]
    
    MMMMM --> PPPPP[Upload Staff Data File]
    NNNNN --> QQQQQ[Select Staff for Update]
    OOOOO --> RRRRR[Select Export Criteria]
    
    PPPPP --> SSSSS[Process Import Data]
    QQQQQ --> TTTTT[Process Bulk Updates]
    RRRRR --> UUUUU[Generate Export File]
    
    SSSSS --> VVVVV[Import Validation & Processing]
    TTTTT --> WWWWW[Update Validation & Processing]
    UUUUU --> XXXXX[Export File Generation]
    
    VVVVV --> YYYYY[Show Import Results]
    WWWWW --> ZZZZZ[Show Update Results]
    XXXXX --> AAAAAA[Download Export File]
    
    YYYYY --> F
    ZZZZZ --> F
    AAAAAA --> F
    
    %% Business Rules
    BBBBBB[Business Rules] --> CCCCCC[Employee ID must be unique]
    CCCCCC --> DDDDDD[Email address must be unique]
    DDDDDD --> EEEEEE[Phone number validation]
    EEEEEE --> FFFFFF[Date of birth validation]
    FFFFFF --> GGGGGG[Joining date validation]
    GGGGGG --> HHHHHH[Department-designation compatibility]
    HHHHHH --> MM
    
    %% Django Implementation
    IIIIII[Django Implementation] --> JJJJJJ[OfficeStaff Model]
    JJJJJJ --> KKKKKK[StaffProfile Model]
    KKKKKK --> LLLLLL[StaffDocument Model]
    LLLLLL --> MMMMMM[StaffForm Validation]
    MMMMMM --> NNNNNN[Staff Management Views]
    NNNNNN --> OOOOOO[Staff Profile Views]
    OOOOOO --> PPPPPP[Staff Reporting Views]
    PPPPPP --> QQQQQQ[SAP Fiori UI Templates]
    QQQQQQ --> RRRRRR[HTMX Dynamic Updates]
    RRRRRR --> F
    
    %% Integration Points
    SSSSSS[Integration Points] --> TTTTTT[Payroll System]
    TTTTTT --> UUUUUU[Attendance System]
    UUUUUU --> VVVVVV[Leave Management]
    VVVVVV --> WWWWWW[Performance Management]
    WWWWWW --> XXXXXX[Asset Management]
    XXXXXX --> YYYYYY[Access Control]
    YYYYYY --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style OO fill:#e8f5e8
    style AAA fill:#e8f5e8
    style RRR fill:#e8f5e8
    style SSS fill:#e8f5e8
    style TTT fill:#e8f5e8
    style UUU fill:#e8f5e8
    style DDDDD fill:#e8f5e8
    style EEEEE fill:#e8f5e8
    style FFFFF fill:#e8f5e8
    style GGGGG fill:#e8f5e8
    style HHHHH fill:#ffebee
    style NN fill:#fff3e0
    style ZZ fill:#fff3e0
    style QQQQ fill:#fff3e0
    style RRRR fill:#fff3e0
    style SSSS fill:#fff3e0
    style TTTT fill:#fff3e0
    style UUUU fill:#fff3e0
    style SSSSS fill:#e8f5e8
    style TTTTT fill:#e8f5e8
    style UUUUU fill:#e8f5e8
    style IIIIII fill:#f1f8e9
    style BBBBBB fill:#e3f2fd
    style SSSSSS fill:#e0f2f1
