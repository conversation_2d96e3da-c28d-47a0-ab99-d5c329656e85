flowchart TD
    A[User Access PayRoll Processing] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load PayRoll Processing Dashboard]
    
    D --> E[Initialize PayRoll Context]
    E --> F[CompId, SessionId, FinYearId]
    F --> G[Display PayRoll Processing Interface]
    G --> H{PayRoll Action}
    
    %% Monthly Payroll Processing
    H -->|Process Monthly Payroll| I[Load Monthly Payroll Processing]
    I --> J[Select Processing Month/Year]
    J --> K[Select Employee Groups]
    K --> L{Group Selection}
    L -->|All Employees| M[Select All Active Employees]
    L -->|Department-wise| N[Select by Department]
    L -->|Business Group| O[Select by Business Group]
    L -->|Individual| P[Select Individual Employees]
    
    M --> Q[Load Employee Salary Data]
    N --> Q
    O --> Q
    P --> Q
    Q --> R[Retrieve Attendance Data]
    R --> S[Calculate Working Days]
    S --> T[Calculate Present Days]
    T --> U[Calculate Leave Days]
    U --> V[Calculate Overtime Hours]
    V --> W[Begin Salary Calculation]
    
    W --> X[Basic Salary Calculation]
    X --> Y[Pro-rata for Partial Month]
    Y --> Z[Allowances Calculation]
    Z --> AA[Vehicle Allowance]
    AA --> BB[LTA Calculation]
    BB --> CC[Ex-Gratia Calculation]
    CC --> DD[Loyalty Bonus]
    DD --> EE[Other Allowances]
    EE --> FF[Overtime Calculation]
    FF --> GG[Gross Salary Calculation]
    
    GG --> HH[Deductions Calculation]
    HH --> II[PF Employee Contribution]
    II --> JJ[PF Company Contribution]
    JJ --> KK[ESI Deduction]
    KK --> LL[Professional Tax]
    LL --> MM[Income Tax (TDS)]
    MM --> NN[Loan EMI Deduction]
    NN --> OO[Other Deductions]
    OO --> PP[Total Deductions]
    
    PP --> QQ[Net Salary Calculation]
    QQ --> RR[Net = Gross - Deductions]
    RR --> SS[Salary Validation]
    SS --> TT{Validation Result}
    TT -->|Failed| UU[Show Validation Errors]
    UU --> I
    TT -->|Passed| VV[Save Payroll Records]
    VV --> WW[Insert into tblHR_Salary_Master]
    WW --> XX[Insert Salary Details]
    XX --> YY[Update Employee Salary Status]
    YY --> ZZ[Generate Salary Slips]
    ZZ --> AAA[Monthly Payroll Complete]
    AAA --> H
    
    %% Individual Salary Processing
    H -->|Process Individual Salary| BBB[Load Individual Salary Processing]
    BBB --> CCC[Search Employee]
    CCC --> DDD[Select Employee]
    DDD --> EEE[Load Employee Salary Configuration]
    EEE --> FFF[Display Current Salary Structure]
    FFF --> GGG[Modify Salary Components if needed]
    GGG --> HHH[Calculate Individual Salary]
    HHH --> III[Apply same calculation logic as monthly]
    III --> JJJ[Save Individual Salary Record]
    JJJ --> KKK[Generate Individual Salary Slip]
    KKK --> H
    
    %% Salary Revision Processing
    H -->|Process Salary Revision| LLL[Load Salary Revision Interface]
    LLL --> MMM[Select Employees for Revision]
    MMM --> NNN[Enter Revision Details]
    NNN --> OOO[Revision Type Selection]
    OOO --> PPP{Revision Type}
    PPP -->|Increment| QQQ[Process Salary Increment]
    PPP -->|Promotion| RRR[Process Promotion Salary]
    PPP -->|Bonus| SSS[Process Bonus Payment]
    PPP -->|Arrears| TTT[Process Salary Arrears]
    
    QQQ --> UUU[Calculate Increment Amount]
    RRR --> VVV[Calculate Promotion Salary]
    SSS --> WWW[Calculate Bonus Amount]
    TTT --> XXX[Calculate Arrears Amount]
    
    UUU --> YYY[Update Salary Master]
    VVV --> YYY
    WWW --> YYY
    XXX --> YYY
    YYY --> ZZZ[Generate Revision Records]
    ZZZ --> AAAA[Salary Revision Complete]
    AAAA --> H
    
    %% Bank Statement Generation
    H -->|Generate Bank Statement| BBBB[Load Bank Statement Generation]
    BBBB --> CCCC[Select Salary Period]
    CCCC --> DDDD[Select Employee Groups]
    DDDD --> EEEE[Retrieve Salary Records]
    EEEE --> FFFF[Calculate Bank Transfer Amounts]
    FFFF --> GGGG[Group by Bank Accounts]
    GGGG --> HHHH[Generate Bank Statement File]
    HHHH --> IIII[Format for Bank Upload]
    IIII --> JJJJ[Generate Statement Report]
    JJJJ --> KKKK[Bank Statement Ready]
    KKKK --> H
    
    %% Payroll Reports
    H -->|Generate Reports| LLLL[PayRoll Reporting Interface]
    LLLL --> MMMM{Report Type}
    MMMM -->|Salary Register| NNNN[Salary Register Report]
    MMMM -->|Department Summary| OOOO[Department-wise Salary Summary]
    MMMM -->|PF Report| PPPP[PF Contribution Report]
    MMMM -->|ESI Report| QQQQ[ESI Contribution Report]
    MMMM -->|Tax Report| RRRR[Income Tax Deduction Report]
    MMMM -->|Bank Report| SSSS[Bank Transfer Report]
    MMMM -->|Variance Report| TTTT[Salary Variance Report]
    
    NNNN --> UUUU[Generate Salary Register]
    OOOO --> VVVV[Generate Department Summary]
    PPPP --> WWWW[Generate PF Report]
    QQQQ --> XXXX[Generate ESI Report]
    RRRR --> YYYY[Generate Tax Report]
    SSSS --> ZZZZ[Generate Bank Report]
    TTTT --> AAAAA[Generate Variance Report]
    
    UUUU --> BBBBB[Export PayRoll Reports]
    VVVV --> BBBBB
    WWWW --> BBBBB
    XXXX --> BBBBB
    YYYY --> BBBBB
    ZZZZ --> BBBBB
    AAAAA --> BBBBB
    BBBBB --> H
    
    %% Payroll Approval Workflow
    H -->|Approve Payroll| CCCCC[PayRoll Approval Interface]
    CCCCC --> DDDDD[Display Pending Payrolls]
    DDDDD --> EEEEE[Review Payroll Summary]
    EEEEE --> FFFFF{Approval Decision}
    FFFFF -->|Approve| GGGGG[Approve Payroll]
    FFFFF -->|Reject| HHHHH[Reject Payroll]
    FFFFF -->|Request Changes| IIIII[Request Payroll Changes]
    
    GGGGG --> JJJJJ[Update Status to Approved]
    HHHHH --> KKKKK[Update Status to Rejected]
    IIIII --> LLLLL[Update Status to Revision Required]
    
    JJJJJ --> MMMMM[Generate Approval Certificate]
    KKKKK --> NNNNN[Send Rejection Notification]
    LLLLL --> OOOOO[Send Revision Request]
    
    MMMMM --> PPPPP[Payroll Approved]
    NNNNN --> H
    OOOOO --> H
    PPPPP --> H
    
    %% Payroll Finalization
    H -->|Finalize Payroll| QQQQQ[PayRoll Finalization]
    QQQQQ --> RRRRR[Verify All Calculations]
    RRRRR --> SSSSS[Check Approval Status]
    SSSSS --> TTTTT[Generate Final Reports]
    TTTTT --> UUUUU[Lock Payroll Period]
    UUUUU --> VVVVV[Archive Payroll Data]
    VVVVV --> WWWWW[Generate Finalization Certificate]
    WWWWW --> XXXXX[Send Finalization Notification]
    XXXXX --> YYYYY[Payroll Finalized]
    YYYYY --> H
    
    %% Payroll Corrections
    H -->|Payroll Corrections| ZZZZZ[PayRoll Correction Interface]
    ZZZZZ --> AAAAAA[Select Employee for Correction]
    AAAAAA --> BBBBBB[Identify Correction Type]
    BBBBBB --> CCCCCC{Correction Type}
    CCCCCC -->|Salary Correction| DDDDDD[Correct Salary Components]
    CCCCCC -->|Attendance Correction| EEEEEE[Correct Attendance Data]
    CCCCCC -->|Deduction Correction| FFFFFF[Correct Deduction Amounts]
    CCCCCC -->|Allowance Correction| GGGGGG[Correct Allowance Amounts]
    
    DDDDDD --> HHHHHH[Recalculate Salary]
    EEEEEE --> HHHHHH
    FFFFFF --> HHHHHH
    GGGGGG --> HHHHHH
    HHHHHH --> IIIIII[Update Corrected Records]
    IIIIII --> JJJJJJ[Generate Correction Report]
    JJJJJJ --> KKKKKK[Payroll Correction Complete]
    KKKKKK --> H
    
    %% Business Rules
    LLLLLL[Business Rules] --> MMMMMM[Minimum wage compliance]
    MMMMMM --> NNNNNN[PF calculation rules]
    NNNNNN --> OOOOOO[ESI calculation rules]
    OOOOOO --> PPPPPP[Tax slab application]
    PPPPPP --> QQQQQQ[Overtime rate rules]
    QQQQQQ --> RRRRRR[Leave encashment rules]
    RRRRRR --> SS
    
    %% Django Implementation
    SSSSSS[Django Implementation] --> TTTTTT[PayrollProcessing Service]
    TTTTTT --> UUUUUU[SalaryCalculation Engine]
    UUUUUU --> VVVVVV[PayrollReport Generator]
    VVVVVV --> WWWWWW[BankStatement Generator]
    WWWWWW --> XXXXXX[PayrollApproval Workflow]
    XXXXXX --> YYYYYY[PayrollCorrection Service]
    YYYYYY --> ZZZZZZ[SAP Fiori PayRoll UI]
    ZZZZZZ --> AAAAAAA[HTMX Dynamic Processing]
    AAAAAAA --> H
    
    %% Integration Points
    BBBBBBB[Integration Points] --> CCCCCCC[Employee Management]
    CCCCCCC --> DDDDDDD[Attendance System]
    DDDDDDD --> EEEEEEE[Leave Management]
    EEEEEEE --> FFFFFFF[Loan Management]
    FFFFFFF --> GGGGGGG[Tax Management]
    GGGGGGG --> HHHHHHH[Banking Integration]
    HHHHHHH --> IIIIIII[Accounts Integration]
    IIIIIII --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style VV fill:#e8f5e8
    style JJJ fill:#e8f5e8
    style YYY fill:#e8f5e8
    style HHHH fill:#e8f5e8
    style GGGGG fill:#e8f5e8
    style HHHHH fill:#ffebee
    style IIIII fill:#fff3e0
    style UUUUU fill:#e8f5e8
    style HHHHHH fill:#e8f5e8
    style UU fill:#fff3e0
    style UUUU fill:#fff3e0
    style VVVV fill:#fff3e0
    style WWWW fill:#fff3e0
    style XXXX fill:#fff3e0
    style YYYY fill:#fff3e0
    style ZZZZ fill:#fff3e0
    style AAAAA fill:#fff3e0
    style SSSSSS fill:#f1f8e9
    style LLLLLL fill:#e3f2fd
    style BBBBBBB fill:#e0f2f1
