graph TD
    A[Machinery Master] --> B[Item Selection]
    A --> C[Machine Management]
    A --> D[Search & Filter]

    %% Item Selection for Machine Registration
    B --> B1[Load Available Items]
    B1 --> B2[Filter Items]
    B2 --> B3{Items Available?}
    B3 -->|Yes| B4[Display Item List]
    B3 -->|No| B5[No Items Message]
    
    B4 --> B6[Search Filters]
    B6 --> B7[Category Filter]
    B6 --> B8[Subcategory Filter]
    B6 --> B9[Item Code Search]
    B6 --> B10[Description Search]
    B6 --> B11[Location Search]
    
    B7 --> B12[Apply Category Filter]
    B8 --> B13[Apply Subcategory Filter]
    B9 --> B14[Apply Item Code Filter]
    B10 --> B15[Apply Description Filter]
    B11 --> B16[Apply Location Filter]
    
    B12 --> B17[Update Item List]
    B13 --> B17
    B14 --> B17
    B15 --> B17
    B16 --> B17
    
    B17 --> B18[Select Item]
    B18 --> B19{Item Selected?}
    B19 -->|Yes| B20[Navigate to Machine Details]
    B19 -->|No| B21[Stay on Selection Page]
    
    B20 --> C1[Machine Details Form]

    %% Machine Details Form Flow
    C1 --> C2[Clear Temp Tables]
    C2 --> C3[Load Item Information]
    C3 --> C4[Basic Machine Information]
    
    C4 --> C5[Make Field]
    C4 --> C6[Model Field]
    C4 --> C7[Capacity Field]
    C4 --> C8[Purchase Date]
    C4 --> C9[Supplier Name]
    C4 --> C10[Cost Field]
    C4 --> C11[Warranty Expiry Date]
    C4 --> C12[Life Date]
    C4 --> C13[Received Date]
    C4 --> C14[Insurance Selection]
    
    C14 --> C15{Insurance Required?}
    C15 -->|Yes| C16[Insurance Expiry Date]
    C15 -->|No| C17[Skip Insurance Fields]
    
    C16 --> C18[Put to Use Date]
    C17 --> C18
    C18 --> C19[Incharge Assignment]
    C19 --> C20[Location Assignment]
    C20 --> C21[PM Days Configuration]
    C21 --> C22[File Attachment]
    
    C22 --> C23[Spare Parts Selection]
    C23 --> C24[Available Spare Items]
    C24 --> C25[Select Spare Parts]
    C25 --> C26[Enter Quantities]
    C26 --> C27[Add to Temp Table]
    C27 --> C28{More Spare Parts?}
    C28 -->|Yes| C25
    C28 -->|No| C29[Process Selection]
    
    C29 --> C30[Available Processes]
    C30 --> C31[Select Processes]
    C31 --> C32[Add to Temp Table]
    C32 --> C33{More Processes?}
    C33 -->|Yes| C31
    C33 -->|No| C34[Form Validation]
    
    C34 --> C35{Validation Passed?}
    C35 -->|No| C36[Show Validation Errors]
    C35 -->|Yes| C37[Check Required Fields]
    
    C36 --> C4
    
    C37 --> C38{Spare Parts Selected?}
    C38 -->|No| C39[Show Spare Parts Error]
    C38 -->|Yes| C40{Processes Selected?}
    
    C39 --> C23
    C40 -->|No| C41[Show Processes Error]
    C40 -->|Yes| C42[Save Machine Master]
    
    C41 --> C29
    
    C42 --> C43[Generate System Date/Time]
    C43 --> C44[Set User & Company Info]
    C44 --> C45[Set Financial Year]
    C45 --> C46[Set Item ID]
    C46 --> C47[Save to Machine Table]
    
    C47 --> C48[Save Spare Parts]
    C48 --> C49[Loop Through Temp Spares]
    C49 --> C50[Create Machine Spare Record]
    C50 --> C51{More Spares?}
    C51 -->|Yes| C49
    C51 -->|No| C52[Save Processes]
    
    C52 --> C53[Loop Through Temp Processes]
    C53 --> C54[Create Machine Process Record]
    C54 --> C55{More Processes?}
    C55 -->|Yes| C53
    C55 -->|No| C56[Clear Temp Tables]
    
    C56 --> C57[Success Message]
    C57 --> C58[Redirect to Machine List]

    %% Machine Management Flow
    C --> D1[Machine List View]
    D1 --> D2[Load Machines]
    D2 --> D3[Apply Company Filter]
    D3 --> D4[Apply Financial Year Filter]
    D4 --> D5[Search Functionality]
    
    D5 --> D6[Category Search]
    D5 --> D7[Subcategory Search]
    D5 --> D8[Item Code Search]
    D5 --> D9[Description Search]
    D5 --> D10[Location Search]
    
    D6 --> D11[Filter by Category]
    D7 --> D12[Filter by Subcategory]
    D8 --> D13[Filter by Item Code]
    D9 --> D14[Filter by Description]
    D10 --> D15[Filter by Location]
    
    D11 --> D16[Display Filtered Results]
    D12 --> D16
    D13 --> D16
    D14 --> D16
    D15 --> D16
    
    D16 --> D17[Machine Actions]
    D17 --> D18[View Details]
    D17 --> D19[Edit Machine]
    D17 --> D20[Delete Machine]
    D17 --> D21[Print Machine]
    
    %% View Machine Details
    D18 --> D22[Load Machine Details]
    D22 --> D23[Display Specifications]
    D23 --> D24[Show Spare Parts]
    D24 --> D25[Show Processes]
    D25 --> D26[Show Maintenance History]
    D26 --> D27[Show Insurance Status]
    D27 --> D28[Show File Attachments]
    
    %% Edit Machine
    D19 --> D29[Load Machine for Edit]
    D29 --> D30[Pre-populate Form]
    D30 --> D31[Allow Modifications]
    D31 --> D32[Update Spare Parts]
    D32 --> D33[Update Processes]
    D33 --> D34[Save Changes]
    D34 --> D35[Update System Date/Time]
    D35 --> D36[Success Message]
    
    %% Delete Machine
    D20 --> D37[Confirm Deletion]
    D37 --> D38{Confirm Delete?}
    D38 -->|No| D16
    D38 -->|Yes| D39[Check Dependencies]
    D39 --> D40{Has Dependencies?}
    D40 -->|Yes| D41[Show Dependency Error]
    D40 -->|No| D42[Delete Machine Record]
    
    D41 --> D16
    D42 --> D43[Delete Spare Parts]
    D43 --> D44[Delete Processes]
    D44 --> D45[Success Message]
    D45 --> D16
    
    %% Print Machine
    D21 --> D46[Generate Print View]
    D46 --> D47[Format Machine Details]
    D47 --> D48[Include Specifications]
    D48 --> D49[Include Spare Parts]
    D49 --> D50[Include Processes]
    D50 --> D51[Generate PDF/Print]

    %% Search & Filter Section
    D --> E1[Advanced Search]
    E1 --> E2[Search Form]
    E2 --> E3[Category Dropdown]
    E2 --> E4[Subcategory Dropdown]
    E2 --> E5[Search Field Selection]
    E2 --> E6[Search Value Input]
    
    E3 --> E7[Load Categories]
    E4 --> E8[Load Subcategories]
    E5 --> E9[Field Options]
    E6 --> E10[Enter Search Term]
    
    E9 --> E11[Item Code Option]
    E9 --> E12[Description Option]
    E9 --> E13[Location Option]
    
    E7 --> E14[Apply Search Filters]
    E8 --> E14
    E10 --> E14
    E11 --> E14
    E12 --> E14
    E13 --> E14
    
    E14 --> E15[Execute Search Query]
    E15 --> E16[Display Search Results]
    E16 --> E17[Pagination]
    E17 --> E18[Result Actions]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class A startClass
    class B3,B19,C15,C28,C33,C35,C38,C40,C51,C55,D38,D40 decisionClass
    class B5,C36,C39,C41,D41 errorClass
    class C57,D36,D45 successClass
    class C42,C47,C48,C52,C56,D22,D29,D42 dataClass
