graph TD
    A[Gate Pass Menu] --> B[Page Load & Initialization]
    A --> C[Gate Pass Creation]
    A --> D[Gate Pass Management]
    A --> E[Gate Pass Tracking]

    %% Page Load & Initialization
    B --> B1[Load Gate Pass Dashboard]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Setup Navigation Menu]
    B3 --> B4[Load Gate Pass Grid]
    B4 --> B5[Apply Company Filter]
    B5 --> B6[Apply Date Filter]
    B6 --> B7[Display Active Gate Passes]
    B7 --> B8[Setup Action Buttons]
    B8 --> B9[Enable Search Functionality]
    B9 --> B10[Page Ready for User Interaction]

    %% Gate Pass Creation
    C --> C1[Gate Pass Type Selection]
    C --> C2[Gate Pass Form Entry]
    C --> C3[Gate Pass Validation]
    C --> C4[Gate Pass Approval]

    %% Gate Pass Type Selection
    C1 --> C1a[Gate Pass Type Interface]
    C1a --> C1b{Gate Pass Type}
    C1b -->|Visitor| C1c[Visitor Gate Pass]
    C1b -->|Material| C1d[Material Gate Pass]
    C1b -->|Vehicle| C1e[Vehicle Gate Pass]
    C1b -->|Employee| C1f[Employee Gate Pass]
    C1b -->|Contractor| C1g[Contractor Gate Pass]
    
    %% Visitor Gate Pass
    C1c --> C1h[Visitor Information Form]
    C1h --> C1i[Visitor Name Entry]
    C1i --> C1j[Visitor Company]
    C1j --> C1k[Contact Number]
    C1k --> C1l[Purpose of Visit]
    C1l --> C1m[Person to Meet]
    C1m --> C1n[Department]
    C1n --> C1o[Expected Duration]
    C1o --> C1p[ID Proof Details]
    C1p --> C1q[Photo Capture]
    C1q --> C2
    
    %% Material Gate Pass
    C1d --> C1r[Material Information Form]
    C1r --> C1s[Material Description]
    C1s --> C1t[Quantity]
    C1t --> C1u[UOM]
    C1u --> C1v[Purpose]
    C1v --> C1w{Material Direction}
    C1w -->|Inward| C1x[Inward Material Details]
    C1w -->|Outward| C1y[Outward Material Details]
    
    C1x --> C1z[Supplier Information]
    C1z --> C1aa[Purchase Order Reference]
    C1aa --> C1bb[Quality Check Required]
    C1bb --> C2
    
    C1y --> C1cc[Customer Information]
    C1cc --> C1dd[Sales Order Reference]
    C1dd --> C1ee[Delivery Details]
    C1ee --> C2
    
    %% Vehicle Gate Pass
    C1e --> C1ff[Vehicle Information Form]
    C1ff --> C1gg[Vehicle Number]
    C1gg --> C1hh[Driver Name]
    C1hh --> C1ii[Driver License]
    C1ii --> C1jj[Vehicle Type]
    C1jj --> C1kk[Purpose]
    C1kk --> C1ll[Load Details]
    C1ll --> C1mm[Entry/Exit Time]
    C1mm --> C2
    
    %% Employee Gate Pass
    C1f --> C1nn[Employee Information Form]
    C1nn --> C1oo[Employee ID]
    C1oo --> C1pp[Employee Name]
    C1pp --> C1qq[Department]
    C1qq --> C1rr[Purpose]
    C1rr --> C1ss[Material Carried]
    C1ss --> C1tt[Authorization Required]
    C1tt --> C2

    %% Gate Pass Form Entry
    C2 --> C2a[Common Information Entry]
    C2a --> C2b[Gate Pass Number Generation]
    C2b --> C2c[Date & Time Entry]
    C2c --> C2d[Valid Until Date]
    C2d --> C2e[Security Guard Assignment]
    C2e --> C2f[Entry Gate Selection]
    C2f --> C2g[Exit Gate Selection]
    C2g --> C2h[Special Instructions]
    C2h --> C2i[Attachment Upload]
    C2i --> C2j[Emergency Contact]
    C2j --> C3

    %% Gate Pass Validation
    C3 --> C3a[Form Validation]
    C3a --> C3b[Required Field Check]
    C3b --> C3c[Data Format Validation]
    C3c --> C3d[Business Rule Validation]
    C3d --> C3e[Security Check]
    C3e --> C3f{Validation Passed?}
    C3f -->|No| C3g[Show Validation Errors]
    C3f -->|Yes| C3h[Save Gate Pass]
    
    C3g --> C2
    C3h --> C3i[Generate Gate Pass Number]
    C3i --> C3j[Set System Information]
    C3j --> C3k[Set Company ID]
    C3k --> C3l[Set Session Details]
    C3l --> C3m[Set Financial Year]
    C3m --> C3n[Set Status - Pending]
    C3n --> C3o[Execute Database Insert]
    C3o --> C3p{Insert Successful?}
    C3p -->|No| C3q[Database Error Message]
    C3p -->|Yes| C3r[Gate Pass Creation Success]
    
    C3q --> C2
    C3r --> C4

    %% Gate Pass Approval
    C4 --> C4a[Approval Workflow]
    C4a --> C4b{Approval Required?}
    C4b -->|No| C4c[Auto-Approve Gate Pass]
    C4b -->|Yes| C4d[Route for Approval]
    
    C4c --> C4e[Set Status - Approved]
    C4d --> C4f[Send to Approver]
    C4f --> C4g[Approval Notification]
    C4g --> C4h[Pending Approval Status]
    
    C4e --> C4i[Generate Gate Pass]
    C4h --> C4j[Wait for Approval]
    C4i --> C4k[Print Gate Pass]
    C4k --> C4l[Send Notifications]
    C4l --> C4m[Return to Dashboard]

    %% Gate Pass Management
    D --> D1[Gate Pass Search & Filter]
    D --> D2[Gate Pass Operations]
    D --> D3[Gate Pass Status Management]
    D --> D4[Gate Pass Reports]

    %% Gate Pass Search & Filter
    D1 --> D1a[Search Interface]
    D1a --> D1b[Gate Pass Number Search]
    D1b --> D1c[Date Range Filter]
    D1c --> D1d[Type Filter]
    D1d --> D1e[Status Filter]
    D1e --> D1f[Person/Company Filter]
    D1f --> D1g[Purpose Filter]
    D1g --> D1h[Apply Search Filters]
    D1h --> D1i[Execute Search Query]
    D1i --> D1j[Display Filtered Results]
    D1j --> D1k[Update Result Count]
    D1k --> D1l[Enable Export Options]

    %% Gate Pass Operations
    D2 --> D2a[View Gate Pass Details]
    D2a --> D2b[Edit Gate Pass]
    D2b --> D2c[Cancel Gate Pass]
    D2c --> D2d[Extend Gate Pass]
    D2d --> D2e[Print Gate Pass]
    D2e --> D2f[Duplicate Gate Pass]
    
    %% View Gate Pass Details
    D2a --> D2g[Load Gate Pass Record]
    D2g --> D2h[Display Complete Information]
    D2h --> D2i[Show Entry/Exit Log]
    D2i --> D2j[Show Approval History]
    D2j --> D2k[Show Related Documents]
    D2k --> D2l[Show Security Notes]
    
    %% Edit Gate Pass
    D2b --> D2m[Check Edit Permission]
    D2m --> D2n{Edit Allowed?}
    D2n -->|No| D2o[Edit Not Allowed Message]
    D2n -->|Yes| D2p[Load Edit Form]
    
    D2o --> D1j
    D2p --> D2q[Pre-populate Fields]
    D2q --> D2r[Allow Modifications]
    D2r --> D2s[Validate Changes]
    D2s --> D2t{Changes Valid?}
    D2t -->|No| D2u[Show Validation Errors]
    D2t -->|Yes| D2v[Update Gate Pass]
    
    D2u --> D2r
    D2v --> D2w[Set Modified Information]
    D2w --> D2x[Execute Database Update]
    D2x --> D2y{Update Successful?}
    D2y -->|No| D2z[Update Error Message]
    D2y -->|Yes| D2aa[Update Success Message]
    
    D2z --> D2r
    D2aa --> D2bb[Send Update Notifications]
    D2bb --> D1j

    %% Gate Pass Status Management
    D3 --> D3a[Status Tracking]
    D3a --> D3b[Pending Gate Passes]
    D3b --> D3c[Approved Gate Passes]
    D3c --> D3d[Active Gate Passes]
    D3d --> D3e[Expired Gate Passes]
    D3e --> D3f[Cancelled Gate Passes]
    D3f --> D3g[Completed Gate Passes]
    D3g --> D3h[Status Change Options]
    D3h --> D3i[Bulk Status Updates]

    %% Gate Pass Reports
    D4 --> D4a[Daily Gate Pass Report]
    D4a --> D4b[Visitor Log Report]
    D4b --> D4c[Material Movement Report]
    D4c --> D4d[Vehicle Log Report]
    D4d --> D4e[Security Report]
    D4e --> D4f[Compliance Report]
    D4f --> D4g[Export Report Options]

    %% Gate Pass Tracking
    E --> E1[Entry Tracking]
    E --> E2[Exit Tracking]
    E --> E3[Duration Monitoring]
    E --> E4[Security Alerts]

    %% Entry Tracking
    E1 --> E1a[Entry Gate Interface]
    E1a --> E1b[Scan Gate Pass]
    E1b --> E1c[Verify Gate Pass]
    E1c --> E1d{Gate Pass Valid?}
    E1d -->|No| E1e[Invalid Gate Pass Alert]
    E1d -->|Yes| E1f[Record Entry Time]
    
    E1e --> E1b
    E1f --> E1g[Capture Entry Photo]
    E1g --> E1h[Security Check]
    E1h --> E1i[Update Entry Status]
    E1i --> E1j[Send Entry Notification]
    E1j --> E1k[Allow Entry]

    %% Exit Tracking
    E2 --> E2a[Exit Gate Interface]
    E2a --> E2b[Scan Gate Pass]
    E2b --> E2c[Verify Exit Permission]
    E2c --> E2d{Exit Allowed?}
    E2d -->|No| E2e[Exit Denied Alert]
    E2d -->|Yes| E2f[Record Exit Time]
    
    E2e --> E2b
    E2f --> E2g[Capture Exit Photo]
    E2g --> E2h[Material Check]
    E2h --> E2i[Security Clearance]
    E2i --> E2j[Update Exit Status]
    E2j --> E2k[Send Exit Notification]
    E2k --> E2l[Complete Gate Pass]

    %% Duration Monitoring
    E3 --> E3a[Calculate Duration]
    E3a --> E3b[Check Time Limits]
    E3b --> E3c{Time Exceeded?}
    E3c -->|Yes| E3d[Generate Time Alert]
    E3c -->|No| E3e[Continue Monitoring]
    
    E3d --> E3f[Notify Security]
    E3e --> E3g[Update Duration Status]
    E3f --> E3h[Escalate if Required]
    E3g --> E3i[Schedule Next Check]

    %% Security Alerts
    E4 --> E4a[Alert Generation]
    E4a --> E4b[Overdue Gate Passes]
    E4b --> E4c[Suspicious Activities]
    E4c --> E4d[Unauthorized Access]
    E4d --> E4e[Emergency Situations]
    E4e --> E4f[Alert Notifications]
    E4f --> E4g[Security Response]
    E4g --> E4h[Incident Logging]

    %% Integration Features
    A --> F[Integration Features]
    F --> F1[HR Integration]
    F1 --> F2[Visitor Management Integration]
    F2 --> F3[Security System Integration]
    F3 --> F4[Inventory Integration]
    F4 --> F5[Vehicle Management Integration]

    %% Workflow Management
    A --> G[Workflow Management]
    G --> G1[Approval Workflows]
    G1 --> G2[Notification System]
    G2 --> G3[Alert Management]
    G3 --> G4[Status Tracking]
    G4 --> G5[Escalation Management]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef creationClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef managementClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef trackingClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B10 initClass
    class C,C1,C2,C3,C4 creationClass
    class D,D1,D2,D3,D4 managementClass
    class E,E1,E2,E3,E4 trackingClass
    class C1b,C1w,C3f,C3p,C4b,D2n,D2t,D2y,E1d,E2d,E3c decisionClass
    class C3g,C3q,D2o,D2u,D2z,E1e,E2e errorClass
    class C3r,D2aa,E1k,E2l successClass
