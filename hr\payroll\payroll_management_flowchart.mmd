flowchart TD
    A[User Access Payroll Management] --> B{Authentication & Session Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Initialize Session Variables]
    D --> E[CompId, SessionId, FinYearId]
    E --> F[Load Payroll Management Dashboard]
    
    F --> G{Payroll Management Action}
    
    %% Employee Search for Payroll
    G -->|Process Payroll| H[Load Employee Search Interface]
    H --> I[Search Criteria Selection]
    I --> J{Search Type}
    J -->|Employee Name| K[Employee Name Search]
    J -->|Department| L[Department-wise Search]
    J -->|Business Group| M[BG Group Search]
    
    K --> N[AutoComplete Employee Search]
    L --> O[Department Filter]
    M --> P[Business Group Filter]
    
    N --> Q[Execute Employee Search]
    O --> Q
    P --> Q
    Q --> R[Display Employee List]
    R --> S[Show Employee Details Grid]
    S --> T[FinYear, EmpId, Name, Dept, BG, Designation]
    T --> U{Select Employee for Payroll}
    
    %% Salary Processing
    U -->|Select Employee| V[Navigate to Salary Details]
    V --> W[Load Salary_New_Details.aspx]
    W --> X[Load Employee Salary Configuration]
    X --> Y[Retrieve Employee Master Data]
    Y --> Z[Get Salary Components]
    
    Z --> AA[Basic Salary Information]
    AA --> BB[Employee Basic Salary]
    BB --> CC[Duty Hours Configuration]
    CC --> DD[Overtime Hours Setup]
    DD --> EE[Overtime Rate Calculation]
    
    Z --> FF[Allowances Calculation]
    FF --> GG[Vehicle Allowance]
    GG --> HH[LTA (Leave Travel Allowance)]
    HH --> II[Ex-Gratia]
    II --> JJ[Loyalty Bonus]
    JJ --> KK[Other Allowances]
    
    Z --> LL[Deductions Calculation]
    LL --> MM[PF Employee Contribution]
    MM --> NN[PF Company Contribution]
    NN --> OO[ESI Deduction]
    OO --> PP[Professional Tax]
    PP --> QQ[Income Tax (TDS)]
    QQ --> RR[Other Deductions]
    
    Z --> SS[Bonus Calculation]
    SS --> TT[Annual Bonus]
    TT --> UU[Attendance Bonus Percentage 1]
    UU --> VV[Attendance Bonus Percentage 2]
    VV --> WW[Performance Bonus]
    
    %% Salary Computation
    KK --> XX[Calculate Gross Salary]
    RR --> XX
    WW --> XX
    XX --> YY[Gross = Basic + Allowances + Bonus]
    YY --> ZZ[Calculate Total Deductions]
    ZZ --> AAA[Total Deductions = PF + ESI + Tax + Others]
    AAA --> BBB[Calculate Net Salary]
    BBB --> CCC[Net = Gross - Deductions]
    
    CCC --> DDD[Salary Validation]
    DDD --> EEE{Validation Check}
    EEE -->|Invalid| FFF[Show Validation Errors]
    FFF --> X
    EEE -->|Valid| GGG[Save Salary Record]
    GGG --> HHH[Insert into tblHR_Salary_Master]
    HHH --> III[Insert Salary Details]
    III --> JJJ[Update Employee Salary Status]
    JJJ --> KKK[Generate Salary Slip]
    
    %% Salary Editing and Management
    G -->|Edit Salary| LLL[Load Salary Edit Interface]
    LLL --> MMM[Search Existing Salary Records]
    MMM --> NNN[Display Salary History]
    NNN --> OOO{Select Salary Record}
    OOO -->|Edit| PPP[Load Salary_Edit_Details.aspx]
    PPP --> QQQ[Modify Salary Components]
    QQQ --> RRR[Recalculate Salary]
    RRR --> SSS[Update Salary Record]
    SSS --> TTT[Log Salary Changes]
    
    OOO -->|Delete| UUU[Confirm Salary Deletion]
    UUU --> VVV{Confirm Delete?}
    VVV -->|No| NNN
    VVV -->|Yes| WWW[Delete Salary Record]
    WWW --> XXX[Update Employee Status]
    XXX --> YYY[Log Deletion Activity]
    
    %% Bank Statement Generation
    G -->|Bank Statement| ZZZ[Load Bank Statement Interface]
    ZZZ --> AAAA[Select Salary Period]
    AAAA --> BBBB[Choose Employees/Departments]
    BBBB --> CCCC[Generate Bank Statement]
    CCCC --> DDDD[Query Salary Records]
    DDDD --> EEEE[Calculate Bank Transfer Amounts]
    EEEE --> FFFF[Format Bank Statement]
    FFFF --> GGGG[Export Bank Statement File]
    GGGG --> HHHH[Generate Statement Report]
    
    %% Payroll Reports
    G -->|Payroll Reports| IIII[Payroll Reporting Interface]
    IIII --> JJJJ{Report Type}
    JJJJ -->|Salary Summary| KKKK[Generate Salary Summary Report]
    JJJJ -->|Department-wise| LLLL[Department Salary Report]
    JJJJ -->|Monthly Summary| MMMM[Monthly Payroll Summary]
    JJJJ -->|Annual Report| NNNN[Annual Salary Report]
    JJJJ -->|Tax Reports| OOOO[Tax Deduction Reports]
    
    KKKK --> PPPP[Export Salary Summary]
    LLLL --> QQQQ[Export Department Report]
    MMMM --> RRRR[Export Monthly Summary]
    NNNN --> SSSS[Export Annual Report]
    OOOO --> TTTT[Export Tax Reports]
    
    %% Bulk Payroll Processing
    G -->|Bulk Processing| UUUU[Bulk Payroll Interface]
    UUUU --> VVVV[Select Processing Period]
    VVVV --> WWWW[Choose Employee Groups]
    WWWW --> XXXX[Department/BG Selection]
    XXXX --> YYYY[Bulk Salary Calculation]
    YYYY --> ZZZZ[Process All Salaries]
    ZZZZ --> AAAAA[Generate Bulk Reports]
    AAAAA --> BBBBB[Bulk Bank Statement]
    BBBBB --> CCCCC[Bulk Processing Complete]
    
    %% Django Implementation
    F --> DDDDD[Django HR Payroll Views]
    DDDDD --> EEEEE[Payroll Models Integration]
    EEEEE --> FFFFF[SalaryForm Validation]
    FFFFF --> GGGGG[Payroll Calculation Engine]
    GGGGG --> HHHHH[Bank Statement Generator]
    HHHHH --> IIIII[Payroll Report Generator]
    IIIII --> JJJJJ[Bulk Processing Engine]
    JJJJJ --> KKKKK[SAP Fiori Payroll UI]
    KKKKK --> LLLLL[HTMX Dynamic Updates]
    LLLLL --> MMMMM[Company Context Integration]
    
    %% Database Schema
    NNNNN[Database Schema] --> OOOOO[tblHR_Salary_Master]
    OOOOO --> PPPPP[tblHR_Salary_Details]
    PPPPP --> QQQQQ[tblHR_OfficeStaff]
    QQQQQ --> RRRRR[tblHR_Offer_Master]
    RRRRR --> SSSSS[tblHR_BankLoan]
    SSSSS --> TTTTT[Payroll Relationships]
    
    %% Salary Calculation Engine
    UUUUU[Calculation Engine] --> VVVVV[Basic Salary Calculation]
    VVVVV --> WWWWW[Allowance Computation]
    WWWWW --> XXXXX[Deduction Calculation]
    XXXXX --> YYYYY[Bonus Computation]
    YYYYY --> ZZZZZ[Tax Calculation]
    ZZZZZ --> AAAAAA[Net Salary Computation]
    
    %% Business Rules
    BBBBBB[Business Rules] --> CCCCCC[Minimum Wage Compliance]
    CCCCCC --> DDDDDD[PF Calculation Rules]
    DDDDDD --> EEEEEE[ESI Calculation Rules]
    EEEEEE --> FFFFFF[Tax Slab Application]
    FFFFFF --> GGGGGG[Overtime Rate Rules]
    GGGGGG --> HHHHHH[Bonus Calculation Rules]
    
    %% Integration Points
    IIIIII[Integration Points] --> JJJJJJ[Employee Management]
    JJJJJJ --> KKKKKK[Attendance System]
    KKKKKK --> LLLLLL[Leave Management]
    LLLLLL --> MMMMMM[Tax Management]
    MMMMMM --> NNNNNN[Banking Integration]
    NNNNNN --> OOOOOO[Compliance Reporting]
    
    %% Security & Permissions
    PPPPPP[Security Layer] --> QQQQQQ[Payroll Data Security]
    QQQQQQ --> RRRRRR[Salary Information Privacy]
    RRRRRR --> SSSSSS[Bank Details Protection]
    SSSSSS --> TTTTTT[Access Control]
    TTTTTT --> UUUUUU[Audit Trail Logging]
    
    %% Error Handling
    VVVVVV[Error Handling] --> WWWWWW[Calculation Validation]
    WWWWWW --> XXXXXX[Data Integrity Checks]
    XXXXXX --> YYYYYY[Business Rule Validation]
    YYYYYY --> ZZZZZZ[User Feedback]
    ZZZZZZ --> AAAAAAA[Exception Management]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#e8f5e8
    style LLL fill:#e8f5e8
    style ZZZ fill:#e8f5e8
    style IIII fill:#e8f5e8
    style UUUU fill:#e8f5e8
    style GGG fill:#e8f5e8
    style SSS fill:#e8f5e8
    style CCCC fill:#fff3e0
    style PPPP fill:#fff3e0
    style QQQQ fill:#fff3e0
    style RRRR fill:#fff3e0
    style SSSS fill:#fff3e0
    style TTTT fill:#fff3e0
    style FFF fill:#fff3e0
    style DDDDD fill:#f1f8e9
    style NNNNN fill:#fafafa
    style UUUUU fill:#e3f2fd
    style BBBBBB fill:#e3f2fd
    style IIIIII fill:#e0f2f1
    style PPPPPP fill:#f3e5f5
    style VVVVVV fill:#fce4ec
