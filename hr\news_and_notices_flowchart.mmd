flowchart TD
    A[User Access News And Notices] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load News And Notices Page]
    
    D --> E[Initialize Session Context]
    E --> F[CompId, SessionId, FinYearId]
    F --> G[Display News And Notices Grid]
    G --> H{User Action}
    
    %% View/Display Operations
    H -->|View Records| I[Query tblHR_NewsandNotices]
    I --> J[Filter by CompId and FinYearId]
    J --> K[Display Grid with Pagination]
    K --> L[Show Title, Date, Type, Status]
    L --> M[Show Edit/Delete/Publish Links]
    M --> H
    
    %% Add New News/Notice
    H -->|Add New| N[Show Add News/Notice Form]
    N --> O[User Enters News/Notice Details]
    O --> P[Title Input]
    P --> Q[Content/Description Input]
    Q --> R[Type Selection (News/Notice)]
    R --> S[Priority Level Selection]
    S --> T[Target Audience Selection]
    T --> U[Publication Date Selection]
    U --> V[Expiry Date Selection]
    V --> W[File Attachment (Optional)]
    W --> X{Validation Check}
    X -->|Empty Fields| Y[Show Required Field Error]
    Y --> O
    X -->|Invalid Dates| Z[Show Date Validation Error]
    Z --> O
    X -->|Valid Data| AA[Insert into tblHR_NewsandNotices]
    AA --> BB[Set SysDate, SysTime, SessionId, CompId, FinYearId]
    BB --> CC[Set Status to Draft]
    CC --> DD{Insert Success?}
    DD -->|Success| EE[Show 'News/Notice Added' Message]
    DD -->|Error| FF[Show Error Message]
    EE --> GG[Refresh Grid Display]
    FF --> GG
    GG --> H
    
    %% Edit News/Notice
    H -->|Edit Record| HH[Enable Edit Mode for Row]
    HH --> II[User Modifies News/Notice Details]
    II --> JJ{Update Validation}
    JJ -->|Invalid| KK[Show Validation Error]
    KK --> II
    JJ -->|Valid| LL[Confirm Update Action]
    LL --> MM{User Confirms?}
    MM -->|No| NN[Cancel Edit Mode]
    NN --> H
    MM -->|Yes| OO[Update tblHR_NewsandNotices Record]
    OO --> PP{Update Success?}
    PP -->|Success| QQ[Show 'Record Updated' Message]
    PP -->|Error| RR[Show Update Error]
    QQ --> SS[Refresh Grid Display]
    RR --> SS
    SS --> H
    
    %% Delete News/Notice
    H -->|Delete Record| TT[Show Delete Confirmation]
    TT --> UU{User Confirms Delete?}
    UU -->|No| H
    UU -->|Yes| VV[Check Publication Status]
    VV --> WW{Already Published?}
    WW -->|Yes| XX[Show 'Cannot Delete - Published' Error]
    XX --> H
    WW -->|No| YY[Delete from tblHR_NewsandNotices]
    YY --> ZZ{Delete Success?}
    ZZ -->|Success| AAA[Show 'Record Deleted' Message]
    ZZ -->|Error| BBB[Show Delete Error]
    AAA --> CCC[Refresh Grid Display]
    BBB --> CCC
    CCC --> H
    
    %% Publish News/Notice
    H -->|Publish| DDD[Load Publication Interface]
    DDD --> EEE[Select News/Notice to Publish]
    EEE --> FFF[Review Publication Details]
    FFF --> GGG[Verify Content and Attachments]
    GGG --> HHH[Select Publication Channels]
    HHH --> III{Publication Channels}
    III -->|Internal Portal| JJJ[Publish to Internal Portal]
    III -->|Email Notification| KKK[Send Email Notifications]
    III -->|SMS Alert| LLL[Send SMS Alerts]
    III -->|Notice Board| MMM[Post to Notice Board]
    
    JJJ --> NNN[Update Portal Content]
    KKK --> OOO[Send Bulk Emails]
    LLL --> PPP[Send Bulk SMS]
    MMM --> QQQ[Update Notice Board]
    
    NNN --> RRR[Update Publication Status]
    OOO --> RRR
    PPP --> RRR
    QQQ --> RRR
    RRR --> SSS[Set Published Date]
    SSS --> TTT[Log Publication Activity]
    TTT --> UUU[Send Publication Confirmation]
    UUU --> H
    
    %% News/Notice Categories
    H -->|Manage Categories| VVV[Load Category Management]
    VVV --> WWW{Category Action}
    WWW -->|Add Category| XXX[Add New Category]
    WWW -->|Edit Category| YYY[Edit Existing Category]
    WWW -->|Delete Category| ZZZ[Delete Category]
    
    XXX --> AAAA[Enter Category Details]
    YYY --> BBBB[Modify Category Details]
    ZZZ --> CCCC[Confirm Category Deletion]
    
    AAAA --> DDDD[Save New Category]
    BBBB --> EEEE[Update Category]
    CCCC --> FFFF[Remove Category]
    
    DDDD --> GGGG[Refresh Category List]
    EEEE --> GGGG
    FFFF --> GGGG
    GGGG --> H
    
    %% Content Management
    H -->|Manage Content| HHHH[Content Management Interface]
    HHHH --> IIII{Content Action}
    IIII -->|Rich Text Editor| JJJJ[Use Rich Text Editor]
    IIII -->|File Attachments| KKKK[Manage File Attachments]
    IIII -->|Image Gallery| LLLL[Manage Image Gallery]
    IIII -->|Document Library| MMMM[Manage Document Library]
    
    JJJJ --> NNNN[Format Content with Rich Editor]
    KKKK --> OOOO[Upload/Manage Attachments]
    LLLL --> PPPP[Upload/Manage Images]
    MMMM --> QQQQ[Upload/Manage Documents]
    
    NNNN --> RRRR[Save Formatted Content]
    OOOO --> RRRR
    PPPP --> RRRR
    QQQQ --> RRRR
    RRRR --> H
    
    %% Approval Workflow
    H -->|Approval Workflow| SSSS[Load Approval Workflow]
    SSSS --> TTTT[Submit for Approval]
    TTTT --> UUUU[Route to Approver]
    UUUU --> VVVV{Approval Decision}
    VVVV -->|Approve| WWWW[Approve News/Notice]
    VVVV -->|Reject| XXXX[Reject News/Notice]
    VVVV -->|Request Changes| YYYY[Request Modifications]
    
    WWWW --> ZZZZ[Update Status to Approved]
    XXXX --> AAAAA[Update Status to Rejected]
    YYYY --> BBBBB[Update Status to Revision Required]
    
    ZZZZ --> CCCCC[Send Approval Notification]
    AAAAA --> DDDDD[Send Rejection Notification]
    BBBBB --> EEEEE[Send Revision Request]
    
    CCCCC --> H
    DDDDD --> H
    EEEEE --> H
    
    %% News/Notice Reports
    H -->|Reports| FFFFF[News/Notice Reporting Interface]
    FFFFF --> GGGGG{Report Type}
    GGGGG -->|Publication Report| HHHHH[Publication Statistics Report]
    GGGGG -->|Readership Report| IIIII[Readership Analytics Report]
    GGGGG -->|Category Report| JJJJJ[Category-wise Report]
    GGGGG -->|Archive Report| KKKKK[Archive Management Report]
    
    HHHHH --> LLLLL[Generate Publication Statistics]
    IIIII --> MMMMM[Generate Readership Analytics]
    JJJJJ --> NNNNN[Generate Category Report]
    KKKKK --> OOOOO[Generate Archive Report]
    
    LLLLL --> PPPPP[Export News/Notice Reports]
    MMMMM --> PPPPP
    NNNNN --> PPPPP
    OOOOO --> PPPPP
    PPPPP --> H
    
    %% Business Rules
    QQQQQ[Business Rules] --> RRRRR[Title is required]
    RRRRR --> SSSSS[Content is required]
    SSSSS --> TTTTT[Publication date validation]
    TTTTT --> UUUUU[Expiry date after publication date]
    UUUUU --> VVVVV[Approval required before publication]
    VVVVV --> WWWWW[File size limits for attachments]
    WWWWW --> X
    
    %% Django Implementation
    XXXXX[Django Implementation] --> YYYYY[NewsNotice Model]
    YYYYY --> ZZZZZ[NewsNoticeCategory Model]
    ZZZZZ --> AAAAAA[NewsNoticeAttachment Model]
    AAAAAA --> BBBBBB[NewsNoticeForm Validation]
    BBBBBB --> CCCCCC[News/Notice Management Views]
    CCCCCC --> DDDDDD[Publication Management Views]
    DDDDDD --> EEEEEE[Content Management Views]
    EEEEEE --> FFFFFF[SAP Fiori UI Templates]
    FFFFFF --> GGGGGG[HTMX Dynamic Updates]
    GGGGGG --> H
    
    %% Integration Points
    HHHHHH[Integration Points] --> IIIIII[Employee Management]
    IIIIII --> JJJJJJ[Email System]
    JJJJJJ --> KKKKKK[SMS Gateway]
    KKKKKK --> LLLLLL[Document Management]
    LLLLLL --> MMMMMM[Approval Workflow]
    MMMMMM --> NNNNNN[Notification System]
    NNNNNN --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style AA fill:#e8f5e8
    style OO fill:#e8f5e8
    style YY fill:#ffebee
    style RRR fill:#e8f5e8
    style DDDD fill:#e8f5e8
    style EEEE fill:#e8f5e8
    style FFFF fill:#e8f5e8
    style RRRR fill:#e8f5e8
    style WWWW fill:#e8f5e8
    style XXXX fill:#ffebee
    style YYYY fill:#fff3e0
    style Y fill:#fff3e0
    style Z fill:#fff3e0
    style XX fill:#fff3e0
    style LLLLL fill:#fff3e0
    style MMMMM fill:#fff3e0
    style NNNNN fill:#fff3e0
    style OOOOO fill:#fff3e0
    style XXXXX fill:#f1f8e9
    style QQQQQ fill:#e3f2fd
    style HHHHHH fill:#e0f2f1
