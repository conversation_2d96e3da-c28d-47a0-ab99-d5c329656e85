flowchart TD
    A[User Access Authorize Gate Pass] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Gate Pass Authorization Page]
    
    D --> E[Initialize Authorization Context]
    E --> F[CompId, SessionId, FinYearId, UserId]
    F --> G[Display Pending Gate Pass Requests]
    G --> H{Authorization Action}
    
    %% View Pending Requests
    H -->|View Pending| I[Query Pending Gate Passes]
    I --> J[Filter by Authorization Level]
    J --> K[Display Pending Requests Grid]
    K --> L[Show Employee, Date, Time, Reason]
    L --> M[Show Authorization Actions]
    M --> H
    
    %% Authorize Gate Pass
    H -->|Authorize Request| N[Select Gate Pass for Authorization]
    N --> O[Load Gate Pass Details]
    O --> P[Display Complete Request Information]
    P --> Q[Employee Information]
    Q --> R[Gate Pass Details]
    R --> S[Reason and Purpose]
    S --> T[Time Duration]
    T --> U[Contact Information]
    U --> V{Authorization Decision}
    
    V -->|Approve| W[Approve Gate Pass]
    V -->|Reject| X[Reject Gate Pass]
    V -->|Request Info| Y[Request Additional Information]
    V -->|Conditional Approve| Z[Conditional Approval]
    
    %% Approve Gate Pass
    W --> AA[Load Approval Form]
    AA --> BB[Approval Comments Input]
    BB --> CC[Approval Conditions (if any)]
    CC --> DD[Authorized Duration Confirmation]
    DD --> EE[Update Gate Pass Status]
    EE --> FF[Set Authorize = 1]
    FF --> GG[Set AuthorizedBy = Current User]
    GG --> HH[Set AuthorizeDate = Current Date]
    HH --> II[Set AuthorizeTime = Current Time]
    II --> JJ[Generate Approved Gate Pass]
    JJ --> KK[Send Approval Notification]
    KK --> LL[Log Approval Activity]
    LL --> MM[Gate Pass Approved Successfully]
    MM --> H
    
    %% Reject Gate Pass
    X --> NN[Load Rejection Form]
    NN --> OO[Rejection Reason Selection]
    OO --> PP[Rejection Comments Input]
    PP --> QQ[Update Gate Pass Status]
    QQ --> RR[Set Authorize = 0]
    RR --> SS[Set AuthorizedBy = Current User]
    SS --> TT[Set AuthorizeDate = Current Date]
    TT --> UU[Set AuthorizeTime = Current Time]
    UU --> VV[Set Rejection Reason]
    VV --> WW[Send Rejection Notification]
    WW --> XX[Log Rejection Activity]
    XX --> YY[Gate Pass Rejected]
    YY --> H
    
    %% Request Additional Information
    Y --> ZZ[Load Information Request Form]
    ZZ --> AAA[Information Required Details]
    AAA --> BBB[Request Comments]
    BBB --> CCC[Set Status to Info Required]
    CCC --> DDD[Send Information Request]
    DDD --> EEE[Log Information Request]
    EEE --> FFF[Information Request Sent]
    FFF --> H
    
    %% Conditional Approval
    Z --> GGG[Load Conditional Approval Form]
    GGG --> HHH[Approval Conditions Input]
    HHH --> III[Modified Duration/Terms]
    III --> JJJ[Conditional Approval Comments]
    JJJ --> KKK[Update Gate Pass with Conditions]
    KKK --> LLL[Set Conditional Approval Status]
    LLL --> MMM[Send Conditional Approval Notice]
    MMM --> NNN[Log Conditional Approval]
    NNN --> OOO[Conditional Approval Complete]
    OOO --> H
    
    %% Bulk Authorization
    H -->|Bulk Authorization| PPP[Load Bulk Authorization]
    PPP --> QQQ[Select Multiple Gate Passes]
    QQQ --> RRR[Bulk Action Selection]
    RRR --> SSS{Bulk Action Type}
    SSS -->|Bulk Approve| TTT[Bulk Approve Selected]
    SSS -->|Bulk Reject| UUU[Bulk Reject Selected]
    
    TTT --> VVV[Process Bulk Approvals]
    UUU --> WWW[Process Bulk Rejections]
    
    VVV --> XXX[Update Multiple Records]
    WWW --> YYY[Update Multiple Records]
    
    XXX --> ZZZ[Send Bulk Notifications]
    YYY --> AAAA[Send Bulk Notifications]
    
    ZZZ --> BBBB[Bulk Authorization Complete]
    AAAA --> BBBB
    BBBB --> H
    
    %% Authorization Reports
    H -->|Authorization Reports| CCCC[Authorization Reporting]
    CCCC --> DDDD{Report Type}
    DDDD -->|Pending Report| EEEE[Pending Authorization Report]
    DDDD -->|Approved Report| FFFF[Approved Gate Pass Report]
    DDDD -->|Rejected Report| GGGG[Rejected Gate Pass Report]
    DDDD -->|Authorizer Report| HHHH[Authorizer Activity Report]
    DDDD -->|Timeline Report| IIII[Authorization Timeline Report]
    
    EEEE --> JJJJ[Generate Pending Report]
    FFFF --> KKKK[Generate Approved Report]
    GGGG --> LLLL[Generate Rejected Report]
    HHHH --> MMMM[Generate Authorizer Report]
    IIII --> NNNN[Generate Timeline Report]
    
    JJJJ --> OOOO[Export Authorization Reports]
    KKKK --> OOOO
    LLLL --> OOOO
    MMMM --> OOOO
    NNNN --> OOOO
    OOOO --> H
    
    %% Authorization Workflow Management
    H -->|Workflow Management| PPPP[Authorization Workflow]
    PPPP --> QQQQ{Workflow Action}
    QQQQ -->|Set Hierarchy| RRRR[Set Authorization Hierarchy]
    QQQQ -->|Delegation| SSSS[Authorization Delegation]
    QQQQ -->|Escalation| TTTT[Escalation Management]
    QQQQ -->|Auto-approval| UUUU[Auto-approval Rules]
    
    RRRR --> VVVV[Configure Authorization Levels]
    SSSS --> WWWW[Setup Delegation Rules]
    TTTT --> XXXX[Configure Escalation Rules]
    UUUU --> YYYY[Setup Auto-approval Criteria]
    
    VVVV --> ZZZZ[Save Workflow Configuration]
    WWWW --> ZZZZ
    XXXX --> ZZZZ
    YYYY --> ZZZZ
    ZZZZ --> H
    
    %% Gate Pass Search and Filter
    H -->|Search & Filter| AAAAA[Gate Pass Search Interface]
    AAAAA --> BBBBB{Search Criteria}
    BBBBB -->|By Employee| CCCCC[Search by Employee Name]
    BBBBB -->|By Date| DDDDD[Search by Date Range]
    BBBBB -->|By Status| EEEEE[Search by Authorization Status]
    BBBBB -->|By Department| FFFFF[Search by Department]
    BBBBB -->|By Reason| GGGGG[Search by Gate Pass Reason]
    
    CCCCC --> HHHHH[Execute Employee Search]
    DDDDD --> IIIII[Execute Date Search]
    EEEEE --> JJJJJ[Execute Status Search]
    FFFFF --> KKKKK[Execute Department Search]
    GGGGG --> LLLLL[Execute Reason Search]
    
    HHHHH --> MMMMM[Display Search Results]
    IIIII --> MMMMM
    JJJJJ --> MMMMM
    KKKKK --> MMMMM
    LLLLL --> MMMMM
    MMMMM --> NNNNN[Show Filtered Gate Pass List]
    NNNNN --> H
    
    %% Business Rules
    OOOOO[Business Rules] --> PPPPP[Authorization level validation]
    PPPPP --> QQQQQ[User authorization rights check]
    QQQQQ --> RRRRR[Gate pass time validation]
    RRRRR --> SSSSS[Approval workflow compliance]
    SSSSS --> TTTTT[Mandatory comments for rejection]
    TTTTT --> UUUUU[Escalation time limits]
    UUUUU --> V
    
    %% Django Implementation
    VVVVV[Django Implementation] --> WWWWW[GatePassAuthorization Model]
    WWWWW --> XXXXX[AuthorizationWorkflow Model]
    XXXXX --> YYYYY[AuthorizationLog Model]
    YYYYY --> ZZZZZ[GatePassAuthorizationForm]
    ZZZZZ --> AAAAAA[Authorization Management Views]
    AAAAAA --> BBBBBB[Workflow Management Views]
    BBBBBB --> CCCCCC[Authorization Reporting Views]
    CCCCCC --> DDDDDD[SAP Fiori UI Templates]
    DDDDDD --> EEEEEE[HTMX Dynamic Updates]
    EEEEEE --> H
    
    %% Integration Points
    FFFFFF[Integration Points] --> GGGGGG[Gate Pass Management]
    GGGGGG --> HHHHHH[Employee Management]
    HHHHHH --> IIIIII[Notification System]
    IIIIII --> JJJJJJ[Email System]
    JJJJJJ --> KKKKKK[SMS System]
    KKKKKK --> LLLLLL[Approval Workflow Engine]
    LLLLLL --> D
    
    %% Notification System
    MMMMMM[Notification System] --> NNNNNN[Email Notifications]
    NNNNNN --> OOOOOO[SMS Notifications]
    OOOOOO --> PPPPPP[In-app Notifications]
    PPPPPP --> QQQQQQ[Push Notifications]
    QQQQQQ --> KK
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style W fill:#e8f5e8
    style X fill:#ffebee
    style Y fill:#fff3e0
    style Z fill:#e8f5e8
    style FF fill:#e8f5e8
    style RR fill:#ffebee
    style CCC fill:#fff3e0
    style KKK fill:#e8f5e8
    style VVV fill:#e8f5e8
    style WWW fill:#ffebee
    style JJJJ fill:#fff3e0
    style KKKK fill:#fff3e0
    style LLLL fill:#fff3e0
    style MMMM fill:#fff3e0
    style NNNN fill:#fff3e0
    style ZZZZ fill:#e8f5e8
    style VVVVV fill:#f1f8e9
    style OOOOO fill:#e3f2fd
    style FFFFFF fill:#e0f2f1
    style MMMMMM fill:#f3e5f5
