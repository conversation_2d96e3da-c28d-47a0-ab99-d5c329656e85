graph TD
    A[Document Management] --> B[Document Upload]
    A --> C[Document List Management]
    A --> D[Document Search & Filter]
    A --> E[File Operations]

    %% Document Upload Flow
    B --> B1[Access Upload Form]
    B1 --> B2[Load Module Dropdown]
    B2 --> B3[Load Available Modules]
    B3 --> B4[Display Module Options]
    B4 --> B5[Select Target Module]
    B5 --> B6{Module Selected?}
    B6 -->|No| B7[Module Selection Required]
    B6 -->|Yes| B8[Format Description Entry]
    
    B7 --> B5
    B8 --> B9[Enter Document Format]
    B9 --> B10[File Selection]
    B10 --> B11[Browse for File]
    B11 --> B12[Select File]
    B12 --> B13{File Selected?}
    B13 -->|No| B14[File Selection Required]
    B13 -->|Yes| B15[File Validation]
    
    B14 --> B11
    B15 --> B16[Check File Size]
    B16 --> B17{Size Valid?}
    B17 -->|No| B18[File Size Error]
    B17 -->|Yes| B19[Check File Type]
    B19 --> B20{Type Valid?}
    B20 -->|No| B21[File Type Error]
    B20 -->|Yes| B22[Security Scan]
    
    B18 --> B11
    B21 --> B11
    B22 --> B23{Virus Free?}
    B23 -->|No| B24[Security Threat Error]
    B23 -->|Yes| B25[Process Upload]
    
    B24 --> B11
    B25 --> B26[Read File Stream]
    B26 --> B27[Convert to Binary]
    B27 --> B28[Extract File Properties]
    B28 --> B29[Generate System Info]
    B29 --> B30[Prepare Database Insert]
    
    B30 --> B31[Set System Date/Time]
    B31 --> B32[Set Company ID]
    B32 --> B33[Set User Session]
    B33 --> B34[Set Financial Year]
    B34 --> B35[Set Module ID]
    B35 --> B36[Set Format Description]
    B36 --> B37[Set File Name]
    B37 --> B38[Set File Size]
    B38 --> B39[Set Content Type]
    B39 --> B40[Set Binary Data]
    
    B40 --> B41[Execute Database Insert]
    B41 --> B42{Insert Successful?}
    B42 -->|No| B43[Database Error]
    B42 -->|Yes| B44[Upload Success]
    
    B43 --> B8
    B44 --> B45[Clear Form]
    B45 --> B46[Show Success Message]
    B46 --> B47[Refresh Document List]

    %% Document List Management
    C --> C1[Load Document List]
    C1 --> C2[Apply Company Filter]
    C2 --> C3[Load Documents from Database]
    C3 --> C4[Enhance with Module Names]
    C4 --> C5[Sort by Module]
    C5 --> C6[Apply Pagination]
    C6 --> C7[Display Document Grid]
    
    C7 --> C8[Document Actions]
    C8 --> C9[View Details]
    C8 --> C10[Download File]
    C8 --> C11[Delete Document]
    C8 --> C12[Edit Information]
    
    %% View Document Details
    C9 --> C13[Load Document Record]
    C13 --> C14[Get Module Information]
    C14 --> C15[Display Document Details]
    C15 --> C16[Show Module Name]
    C16 --> C17[Show Format Description]
    C17 --> C18[Show File Name]
    C18 --> C19[Show File Size]
    C19 --> C20[Show Content Type]
    C20 --> C21[Show Upload Date/Time]
    C21 --> C22[Show Uploaded By]
    C22 --> C23[Show Company Info]
    
    %% Download File
    C10 --> C24[Validate Download Request]
    C24 --> C25[Check User Permissions]
    C25 --> C26{Permission Valid?}
    C26 -->|No| C27[Access Denied Error]
    C26 -->|Yes| C28[Load Document Record]
    
    C27 --> C7
    C28 --> C29[Check File Exists]
    C29 --> C30{File Exists?}
    C30 -->|No| C31[File Not Found Error]
    C30 -->|Yes| C32[Retrieve Binary Data]
    
    C31 --> C7
    C32 --> C33[Set Response Headers]
    C33 --> C34[Set Content Type]
    C34 --> C35[Set Content Disposition]
    C35 --> C36[Set File Name]
    C36 --> C37[Stream File to Browser]
    C37 --> C38[Log Download Activity]
    C38 --> C39[Update Download Counter]
    
    %% Delete Document
    C11 --> C40[Confirm Deletion]
    C40 --> C41{Confirm Delete?}
    C41 -->|No| C7
    C41 -->|Yes| C42[Check User Permissions]
    C42 --> C43{Delete Permission?}
    C43 -->|No| C44[Permission Denied]
    C43 -->|Yes| C45[Check Dependencies]
    
    C44 --> C7
    C45 --> C46{Has Dependencies?}
    C46 -->|Yes| C47[Dependency Error]
    C46 -->|No| C48[Delete Document Record]
    
    C47 --> C7
    C48 --> C49{Delete Successful?}
    C49 -->|No| C50[Delete Error]
    C49 -->|Yes| C51[Delete Success]
    
    C50 --> C7
    C51 --> C52[Show Success Message]
    C52 --> C53[Refresh Document List]

    %% Document Search & Filter
    D --> D1[Search Interface]
    D1 --> D2[Module Filter Dropdown]
    D1 --> D3[Format Search Box]
    D1 --> D4[Date Range Picker]
    D1 --> D5[File Type Filter]
    
    D2 --> D6[Select Module Filter]
    D6 --> D7{Module Selected?}
    D7 -->|Yes| D8[Apply Module Filter]
    D7 -->|No| D9[Show All Modules]
    
    D3 --> D10[Enter Format Search]
    D10 --> D11{Search Term Entered?}
    D11 -->|Yes| D12[Apply Format Filter]
    D11 -->|No| D13[Show All Formats]
    
    D4 --> D14[Select Date Range]
    D14 --> D15{Date Range Selected?}
    D15 -->|Yes| D16[Apply Date Filter]
    D15 -->|No| D17[Show All Dates]
    
    D5 --> D18[Select File Type]
    D18 --> D19{File Type Selected?}
    D19 -->|Yes| D20[Apply Type Filter]
    D19 -->|No| D21[Show All Types]
    
    D8 --> D22[Combine Filters]
    D9 --> D22
    D12 --> D22
    D13 --> D22
    D16 --> D22
    D17 --> D22
    D20 --> D22
    D21 --> D22
    
    D22 --> D23[Execute Search Query]
    D23 --> D24[Apply Company Filter]
    D24 --> D25[Sort Results]
    D25 --> D26[Apply Pagination]
    D26 --> D27[Display Search Results]
    D27 --> D28[Show Result Count]
    D28 --> D29[Enable Result Actions]

    %% File Operations
    E --> E1[File Upload Processing]
    E --> E2[File Download Handling]
    E --> E3[File Validation]
    E --> E4[File Storage Management]
    
    %% File Upload Processing
    E1 --> E1a[Receive File Upload]
    E1a --> E1b[Create File Stream]
    E1b --> E1c[Read File Content]
    E1c --> E1d[Convert to Byte Array]
    E1d --> E1e[Extract File Metadata]
    E1e --> E1f[Validate File Properties]
    E1f --> E1g[Prepare for Storage]
    E1g --> E1h[Store in Database]
    
    %% File Download Handling
    E2 --> E2a[Validate Request]
    E2a --> E2b[Retrieve File Data]
    E2b --> E2c[Prepare Response]
    E2c --> E2d[Set Headers]
    E2d --> E2e[Stream to Client]
    E2e --> E2f[Log Activity]
    
    %% File Validation
    E3 --> E3a[Size Validation]
    E3a --> E3b[Type Validation]
    E3b --> E3c[Content Validation]
    E3c --> E3d[Security Validation]
    E3d --> E3e[Integrity Check]
    
    %% File Storage Management
    E4 --> E4a[Database Storage]
    E4a --> E4b[Binary Data Handling]
    E4b --> E4c[Metadata Management]
    E4c --> E4d[Index Optimization]
    E4d --> E4e[Cleanup Operations]

    %% HTMX Real-time Features
    A --> F[Real-time Features]
    F --> F1[Live Search]
    F1 --> F1a[Instant Results]
    F1a --> F1b[No Page Refresh]
    
    F --> F2[Dynamic Filtering]
    F2 --> F2a[Real-time Filter Updates]
    F2a --> F2b[Instant Result Updates]
    
    F --> F3[Upload Progress]
    F3 --> F3a[Progress Bar]
    F3a --> F3b[Status Updates]
    
    F --> F4[Auto-refresh Lists]
    F4 --> F4a[Periodic Updates]
    F4a --> F4b[New Document Notifications]

    %% Error Handling
    A --> G[Error Handling]
    G --> G1[Upload Errors]
    G1 --> G1a[File Size Errors]
    G1a --> G1b[File Type Errors]
    G1b --> G1c[Security Errors]
    G1c --> G1d[Database Errors]
    
    G --> G2[Download Errors]
    G2 --> G2a[File Not Found]
    G2a --> G2b[Permission Denied]
    G2b --> G2c[Corrupted File]
    
    G --> G3[System Errors]
    G3 --> G3a[Database Connection]
    G3a --> G3b[Server Errors]
    G3b --> G3c[Network Issues]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef uploadClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef listClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef searchClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef fileClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B25,B40,B44 uploadClass
    class C,C1,C7,C13,C28 listClass
    class D,D1,D22,D23,D27 searchClass
    class E,E1,E2,E3,E4 fileClass
    class B6,B13,B17,B20,B23,B42,C26,C30,C41,C43,C46,C49,D7,D11,D15,D19 decisionClass
    class B18,B21,B24,B43,C27,C31,C44,C47,C50 errorClass
    class B44,B46,C51,C52 successClass
