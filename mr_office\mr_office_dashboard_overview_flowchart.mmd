graph TD
    A[MR Office Dashboard] --> B[Document Overview]
    A --> C[Quick Actions]
    A --> D[Recent Activity]
    A --> E[Storage Statistics]
    A --> F[Module Summary]

    %% Document Overview Section
    B --> B1[Total Documents Count]
    B1 --> B2[Load All Documents]
    B2 --> B3[Apply Company Filter]
    B3 --> B4[Count Documents]
    B4 --> B5[Display Total Count]
    B5 --> B6[Calculate Growth Rate]
    B6 --> B7[Show Growth Indicator]
    
    B --> B8[Documents by Status]
    B8 --> B9[Active Documents]
    B9 --> B10[Archived Documents]
    B10 --> B11[Pending Approval]
    B11 --> B12[Status Distribution Chart]
    
    B --> B13[File Type Distribution]
    B13 --> B14[Group by Content Type]
    B14 --> B15[Count by Type]
    B15 --> B16[Calculate Percentages]
    B16 --> B17[Generate Pie Chart]
    B17 --> B18[Display Type Distribution]
    
    B --> B19[Upload Trends]
    B19 --> B20[Last 30 Days Uploads]
    B20 --> B21[Weekly Upload Trends]
    B21 --> B22[Monthly Comparisons]
    B22 --> B23[Generate Trend Chart]
    B23 --> B24[Display Upload Trends]

    %% Quick Actions Section
    C --> C1[Upload New Document]
    C1 --> C1a[Navigate to Upload Form]
    C1a --> C1b[Pre-select Module]
    C1b --> C1c[Quick Upload Interface]
    
    C --> C2[Search Documents]
    C2 --> C2a[Open Search Interface]
    C2a --> C2b[Advanced Search Options]
    C2b --> C2c[Real-time Search Results]
    
    C --> C3[Browse by Module]
    C3 --> C3a[Module Selection Grid]
    C3a --> C3b[Module Document Counts]
    C3b --> C3c[Navigate to Module Documents]
    
    C --> C4[Recent Documents]
    C4 --> C4a[Last 10 Documents]
    C4a --> C4b[Quick Access Links]
    C4b --> C4c[Document Preview]
    
    C --> C5[Bulk Operations]
    C5 --> C5a[Bulk Download]
    C5a --> C5b[Bulk Delete]
    C5b --> C5c[Bulk Archive]
    
    C --> C6[Generate Reports]
    C6 --> C6a[Quick Report Options]
    C6a --> C6b[Custom Report Builder]
    C6b --> C6c[Scheduled Reports]

    %% Recent Activity Section
    D --> D1[Recent Uploads]
    D1 --> D2[Load Recent Upload Events]
    D2 --> D3[Last 24 Hours]
    D3 --> D4[Display Upload List]
    D4 --> D5[Show Uploader Info]
    D5 --> D6[Show File Details]
    D6 --> D7[Quick Action Buttons]
    
    D --> D8[Recent Downloads]
    D8 --> D9[Load Recent Download Events]
    D9 --> D10[Track Download Activity]
    D10 --> D11[Display Download List]
    D11 --> D12[Show Downloader Info]
    D12 --> D13[Show Access Time]
    
    D --> D14[User Activity Feed]
    D14 --> D15[Aggregate User Actions]
    D15 --> D16[Upload Activities]
    D16 --> D17[Download Activities]
    D17 --> D18[Delete Activities]
    D18 --> D19[Search Activities]
    D19 --> D20[Display Activity Timeline]
    
    D --> D21[System Events]
    D21 --> D22[System Notifications]
    D22 --> D23[Error Events]
    D23 --> D24[Warning Events]
    D24 --> D25[Success Events]
    D25 --> D26[Display Event Log]

    %% Storage Statistics Section
    E --> E1[Storage Utilization]
    E1 --> E2[Calculate Total Storage Used]
    E2 --> E3[Calculate Available Storage]
    E3 --> E4[Calculate Utilization Percentage]
    E4 --> E5[Display Storage Gauge]
    E5 --> E6[Storage Trend Chart]
    
    E --> E7[Storage by Module]
    E7 --> E8[Calculate Module Storage]
    E8 --> E9[Rank Modules by Size]
    E9 --> E10[Generate Module Chart]
    E10 --> E11[Display Module Storage]
    
    E --> E12[File Size Analysis]
    E12 --> E13[Average File Size]
    E13 --> E14[Largest Files]
    E14 --> E15[Smallest Files]
    E15 --> E16[Size Distribution]
    E16 --> E17[Display Size Statistics]
    
    E --> E18[Storage Alerts]
    E18 --> E19{Storage > 80%?}
    E19 -->|Yes| E20[High Usage Alert]
    E19 -->|No| E21[Normal Usage]
    E20 --> E22[Cleanup Recommendations]
    E21 --> E23[Continue Monitoring]
    
    E22 --> E24[Identify Large Files]
    E24 --> E25[Identify Old Files]
    E25 --> E26[Suggest Cleanup Actions]
    E26 --> E27[Display Cleanup Options]

    %% Module Summary Section
    F --> F1[Module Statistics]
    F1 --> F2[Documents per Module]
    F2 --> F3[Load Module Master]
    F3 --> F4[Count Documents by Module]
    F4 --> F5[Calculate Module Percentages]
    F5 --> F6[Generate Module Grid]
    F6 --> F7[Display Module Cards]
    
    F --> F8[Popular Modules]
    F8 --> F9[Rank by Document Count]
    F9 --> F10[Rank by Upload Activity]
    F10 --> F11[Rank by Download Activity]
    F11 --> F12[Generate Popularity Chart]
    F12 --> F13[Display Popular Modules]
    
    F --> F14[Module Activity]
    F14 --> F15[Recent Module Activity]
    F15 --> F16[Module Upload Trends]
    F16 --> F17[Module Access Patterns]
    F17 --> F18[Generate Activity Summary]
    F18 --> F19[Display Module Activity]
    
    F --> F20[Module Health]
    F20 --> F21[Check Module Accessibility]
    F21 --> F22[Verify Module Permissions]
    F22 --> F23[Check Module Configuration]
    F23 --> F24{Module Issues?}
    F24 -->|Yes| F25[Display Module Warnings]
    F24 -->|No| F26[All Modules Healthy]
    
    F25 --> F27[Module Issue Details]
    F26 --> F28[Continue Monitoring]

    %% Real-time Updates
    A --> G[Real-time Updates]
    G --> G1[Auto-refresh Dashboard]
    G1 --> G2[Update Every 30 Seconds]
    G2 --> G3[Refresh Statistics]
    G3 --> G4[Refresh Activity Feed]
    G4 --> G5[Refresh Storage Info]
    G5 --> G6[Update Notifications]
    
    G --> G7[WebSocket Connections]
    G7 --> G8[Live Activity Updates]
    G8 --> G9[Real-time Notifications]
    G9 --> G10[Instant Status Updates]
    
    G --> G11[Push Notifications]
    G11 --> G12[Browser Notifications]
    G12 --> G13[Email Alerts]
    G13 --> G14[SMS Notifications]

    %% Dashboard Customization
    A --> H[Dashboard Customization]
    H --> H1[Widget Configuration]
    H1 --> H2[Show/Hide Widgets]
    H2 --> H3[Resize Widgets]
    H3 --> H4[Rearrange Layout]
    H4 --> H5[Save User Preferences]
    
    H --> H6[Theme Selection]
    H6 --> H7[Light Theme]
    H6 --> H8[Dark Theme]
    H6 --> H9[Custom Theme]
    
    H --> H10[Dashboard Views]
    H10 --> H11[Executive View]
    H10 --> H12[Manager View]
    H10 --> H13[User View]
    H10 --> H14[Admin View]

    %% Mobile Dashboard
    A --> I[Mobile Dashboard]
    I --> I1[Responsive Design]
    I1 --> I2[Touch-friendly Interface]
    I2 --> I3[Swipe Navigation]
    I3 --> I4[Mobile Upload]
    I4 --> I5[Offline Capability]
    
    I --> I6[Mobile-specific Features]
    I6 --> I7[Camera Upload]
    I7 --> I8[Voice Notes]
    I8 --> I9[GPS Tagging]
    I9 --> I10[Mobile Notifications]

    %% Performance Monitoring
    A --> J[Performance Monitoring]
    J --> J1[Dashboard Load Time]
    J1 --> J2[Widget Performance]
    J2 --> J3[Query Optimization]
    J3 --> J4[Caching Strategy]
    J4 --> J5[Resource Usage]
    
    J --> J6[Performance Alerts]
    J6 --> J7{Performance Issues?}
    J7 -->|Yes| J8[Performance Warnings]
    J7 -->|No| J9[Optimal Performance]
    
    J8 --> J10[Optimization Suggestions]
    J9 --> J11[Continue Monitoring]

    %% Security Dashboard
    A --> K[Security Dashboard]
    K --> K1[Access Monitoring]
    K1 --> K2[Failed Login Attempts]
    K2 --> K3[Suspicious Activities]
    K3 --> K4[Security Alerts]
    
    K --> K5[Compliance Status]
    K5 --> K6[Data Protection Status]
    K6 --> K7[Audit Compliance]
    K7 --> K8[Security Score]

    %% Integration Status
    A --> L[Integration Status]
    L --> L1[Module Connectivity]
    L1 --> L2[API Status]
    L2 --> L3[Database Connectivity]
    L3 --> L4[External Services]
    L4 --> L5[Integration Health]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef overviewClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef actionClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef activityClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storageClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef moduleClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef alertClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B8,B13,B19 overviewClass
    class C,C1,C2,C3,C4,C5,C6 actionClass
    class D,D1,D8,D14,D21 activityClass
    class E,E1,E7,E12,E18 storageClass
    class F,F1,F8,F14,F20 moduleClass
    class E19,F24,J7 decisionClass
    class E20,F25,J8 alertClass
    class E21,F26,J9 successClass
