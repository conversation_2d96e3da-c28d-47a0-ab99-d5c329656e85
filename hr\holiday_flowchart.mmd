flowchart TD
    A[User Access Holiday Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Holiday Master Page]
    
    D --> E[Initialize Session Context]
    E --> F[CompId, SessionId, FinYearId]
    F --> G[Display Holiday Grid]
    G --> H{User Action}
    
    %% View/Display Operations
    H -->|View Records| I[Query tblHR_Holiday_Master]
    I --> J[Filter by CompId and FinYearId]
    J --> K[Display Grid with Pagination]
    K --> L[Show Holiday Date & Title]
    L --> M[Show Edit/Delete Links]
    M --> H
    
    %% Add New Holiday
    H -->|Add New Holiday| N[Show Add Holiday Form]
    N --> O[User Enters Holiday Details]
    O --> P[Holiday Date Selection]
    P --> Q[Holiday Title Input]
    Q --> R{Validation Check}
    R -->|Empty Fields| S[Show Required Field Error]
    S --> O
    R -->|Invalid Date| T[Show Date Format Error]
    T --> O
    R -->|Valid Data| U[Check Holiday Date Uniqueness]
    U --> V{Holiday Date Exists?}
    V -->|Yes| W[Show 'Holiday Already Exists for Date' Alert]
    W --> O
    V -->|No| X[Insert into tblHR_Holiday_Master]
    X --> Y[Set SysDate, SysTime, SessionId, CompId, FinYearId]
    Y --> Z{Insert Success?}
    Z -->|Success| AA[Show 'Holiday Added' Message]
    Z -->|Error| BB[Show Error Message]
    AA --> CC[Refresh Grid Display]
    BB --> CC
    CC --> H
    
    %% Edit Holiday
    H -->|Edit Record| DD[Enable Edit Mode for Row]
    DD --> EE[User Modifies Holiday Details]
    EE --> FF{Update Validation}
    FF -->|Invalid| GG[Show Validation Error]
    GG --> EE
    FF -->|Valid| HH[Confirm Update Action]
    HH --> II{User Confirms?}
    II -->|No| JJ[Cancel Edit Mode]
    JJ --> H
    II -->|Yes| KK[Update tblHR_Holiday_Master Record]
    KK --> LL{Update Success?}
    LL -->|Success| MM[Show 'Record Updated' Message]
    LL -->|Error| NN[Show Update Error]
    MM --> OO[Refresh Grid Display]
    NN --> OO
    OO --> H
    
    %% Delete Holiday
    H -->|Delete Record| PP[Show Delete Confirmation]
    PP --> QQ{User Confirms Delete?}
    QQ -->|No| H
    QQ -->|Yes| RR[Check Holiday Usage]
    RR --> SS{Holiday in Use?}
    SS -->|Yes| TT[Show 'Cannot Delete - In Use' Error]
    TT --> H
    SS -->|No| UU[Delete from tblHR_Holiday_Master]
    UU --> VV{Delete Success?}
    VV -->|Success| WW[Show 'Record Deleted' Message]
    VV -->|Error| XX[Show Delete Error]
    WW --> YY[Refresh Grid Display]
    XX --> YY
    YY --> H
    
    %% Holiday Calendar Management
    H -->|Manage Calendar| ZZ[Load Holiday Calendar View]
    ZZ --> AAA[Display Calendar Interface]
    AAA --> BBB[Show Monthly Calendar]
    BBB --> CCC[Highlight Holiday Dates]
    CCC --> DDD{Calendar Action}
    
    DDD -->|Add Holiday| EEE[Click Date on Calendar]
    EEE --> FFF[Quick Add Holiday Form]
    FFF --> GGG[Enter Holiday Title]
    GGG --> HHH[Save Holiday]
    HHH --> III[Update Calendar Display]
    III --> H
    
    DDD -->|Edit Holiday| JJJ[Click Holiday on Calendar]
    JJJ --> KKK[Edit Holiday Details]
    KKK --> LLL[Update Holiday]
    LLL --> MMM[Refresh Calendar]
    MMM --> H
    
    DDD -->|View Month| NNN[Navigate Calendar Months]
    NNN --> OOO[Load Different Month]
    OOO --> BBB
    
    %% Holiday Types Management
    H -->|Manage Types| PPP[Load Holiday Types]
    PPP --> QQQ{Holiday Type}
    QQQ -->|National Holiday| RRR[National Holiday Configuration]
    QQQ -->|Regional Holiday| SSS[Regional Holiday Configuration]
    QQQ -->|Company Holiday| TTT[Company Holiday Configuration]
    QQQ -->|Optional Holiday| UUU[Optional Holiday Configuration]
    
    RRR --> VVV[Set National Holiday Rules]
    SSS --> WWW[Set Regional Holiday Rules]
    TTT --> XXX[Set Company Holiday Rules]
    UUU --> YYY[Set Optional Holiday Rules]
    
    VVV --> ZZZ[Configure Holiday Policies]
    WWW --> ZZZ
    XXX --> ZZZ
    YYY --> ZZZ
    ZZZ --> AAAA[Save Holiday Type Configuration]
    AAAA --> H
    
    %% Holiday Reports
    H -->|Holiday Reports| BBBB[Holiday Reporting Interface]
    BBBB --> CCCC{Report Type}
    CCCC -->|Annual Calendar| DDDD[Annual Holiday Calendar]
    CCCC -->|Monthly Report| EEEE[Monthly Holiday Report]
    CCCC -->|Holiday List| FFFF[Complete Holiday List]
    CCCC -->|Working Days| GGGG[Working Days Analysis]
    
    DDDD --> HHHH[Generate Annual Calendar]
    EEEE --> IIII[Generate Monthly Report]
    FFFF --> JJJJ[Generate Holiday List]
    GGGG --> KKKK[Generate Working Days Report]
    
    HHHH --> LLLL[Export Holiday Reports]
    IIII --> LLLL
    JJJJ --> LLLL
    KKKK --> LLLL
    LLLL --> H
    
    %% Holiday Notifications
    H -->|Notifications| MMMM[Holiday Notification Management]
    MMMM --> NNNN{Notification Type}
    NNNN -->|Upcoming Holidays| OOOO[Upcoming Holiday Alerts]
    NNNN -->|Holiday Reminders| PPPP[Holiday Reminder System]
    NNNN -->|Calendar Updates| QQQQ[Calendar Update Notifications]
    
    OOOO --> RRRR[Send Upcoming Holiday Notifications]
    PPPP --> SSSS[Send Holiday Reminders]
    QQQQ --> TTTT[Send Calendar Update Alerts]
    
    RRRR --> UUUU[Log Notification Activity]
    SSSS --> UUUU
    TTTT --> UUUU
    UUUU --> H
    
    %% Business Rules
    VVVV[Business Rules] --> WWWW[Holiday date must be unique per company]
    WWWW --> XXXX[Holiday title is required]
    XXXX --> YYYY[Date format validation]
    YYYY --> ZZZZ[Future date validation]
    ZZZZ --> AAAAA[Company context required]
    AAAAA --> BBBBB[Financial year context required]
    BBBBB --> R
    
    %% Django Implementation
    CCCCC[Django Implementation] --> DDDDD[Holiday Model]
    DDDDD --> EEEEE[HolidayType Model]
    EEEEE --> FFFFF[HolidayCalendar Model]
    FFFFF --> GGGGG[HolidayForm Validation]
    GGGGG --> HHHHH[Holiday Management Views]
    HHHHH --> IIIII[Calendar Management Views]
    IIIII --> JJJJJ[Holiday Reporting Views]
    JJJJJ --> KKKKK[SAP Fiori UI Templates]
    KKKKK --> LLLLL[HTMX Dynamic Updates]
    LLLLL --> H
    
    %% Integration Points
    MMMMM[Integration Points] --> NNNNN[Attendance System]
    NNNNN --> OOOOO[Leave Management]
    OOOOO --> PPPPP[Payroll Processing]
    PPPPP --> QQQQQ[Working Days Calculation]
    QQQQQ --> RRRRR[Overtime Calculation]
    RRRRR --> SSSSS[Shift Management]
    SSSSS --> D
    
    %% Calendar Features
    TTTTT[Calendar Features] --> UUUUU[Interactive Calendar View]
    UUUUU --> VVVVV[Drag & Drop Holiday Management]
    VVVVV --> WWWWW[Multi-month View]
    WWWWW --> XXXXX[Holiday Color Coding]
    XXXXX --> YYYYY[Quick Holiday Addition]
    YYYYY --> ZZZZZ[Holiday Search & Filter]
    ZZZZZ --> ZZ
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style X fill:#e8f5e8
    style KK fill:#e8f5e8
    style UU fill:#ffebee
    style HHH fill:#e8f5e8
    style LLL fill:#e8f5e8
    style AAAA fill:#e8f5e8
    style W fill:#fff3e0
    style S fill:#fff3e0
    style T fill:#fff3e0
    style TT fill:#fff3e0
    style ZZ fill:#e8f5e8
    style HHHH fill:#fff3e0
    style IIII fill:#fff3e0
    style JJJJ fill:#fff3e0
    style KKKK fill:#fff3e0
    style RRRR fill:#e8f5e8
    style SSSS fill:#e8f5e8
    style TTTT fill:#e8f5e8
    style CCCCC fill:#f1f8e9
    style VVVV fill:#e3f2fd
    style MMMMM fill:#e0f2f1
    style TTTTT fill:#f3e5f5
