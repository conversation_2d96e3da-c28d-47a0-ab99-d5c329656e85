graph TD
    A[Scheduler Module] --> B[Scheduler Menu]
    A --> C[Gate Pass Menu]
    A --> D[IOU Menu]

    %% Scheduler Menu
    B --> B1[Production Scheduling]
    B --> B2[Resource Planning]
    B --> B3[Time Management]
    B --> B4[Schedule Optimization]

    %% Production Scheduling
    B1 --> B1a[Work Order Scheduling]
    B1a --> B1b[Machine Allocation]
    B1b --> B1c[Operator Assignment]
    B1c --> B1d[Material Planning]
    B1d --> B1e[Timeline Creation]

    %% Resource Planning
    B2 --> B2a[Resource Availability]
    B2a --> B2b[Capacity Planning]
    B2b --> B2c[Bottleneck Analysis]
    B2c --> B2d[Resource Optimization]

    %% Time Management
    B3 --> B3a[Shift Planning]
    B3a --> B3b[Break Scheduling]
    B3b --> B3c[Overtime Management]
    B3c --> B3d[Holiday Planning]

    %% Schedule Optimization
    B4 --> B4a[Priority Management]
    B4a --> B4b[Deadline Tracking]
    B4b --> B4c[Efficiency Analysis]
    B4c --> B4d[Performance Metrics]

    %% Gate Pass Menu
    C --> C1[Gate Pass Creation]
    C --> C2[Gate Pass Management]
    C --> C3[Gate Pass Tracking]
    C --> C4[Gate Pass Reports]

    %% Gate Pass Creation
    C1 --> C1a[Visitor Gate Pass]
    C1a --> C1b[Material Gate Pass]
    C1b --> C1c[Vehicle Gate Pass]
    C1c --> C1d[Employee Gate Pass]

    %% Gate Pass Management
    C2 --> C2a[Gate Pass Approval]
    C2a --> C2b[Gate Pass Modification]
    C2b --> C2c[Gate Pass Cancellation]
    C2c --> C2d[Gate Pass Renewal]

    %% Gate Pass Tracking
    C3 --> C3a[Entry Tracking]
    C3a --> C3b[Exit Tracking]
    C3b --> C3c[Duration Monitoring]
    C3c --> C3d[Security Alerts]

    %% Gate Pass Reports
    C4 --> C4a[Daily Gate Pass Report]
    C4a --> C4b[Visitor Log Report]
    C4b --> C4c[Material Movement Report]
    C4c --> C4d[Security Report]

    %% IOU Menu
    D --> D1[IOU Creation]
    D --> D2[IOU Management]
    D --> D3[IOU Tracking]
    D --> D4[IOU Settlement]

    %% IOU Creation
    D1 --> D1a[Employee IOU]
    D1a --> D1b[Vendor IOU]
    D1b --> D1c[Advance IOU]
    D1c --> D1d[Expense IOU]

    %% IOU Management
    D2 --> D2a[IOU Approval]
    D2a --> D2b[IOU Modification]
    D2b --> D2c[IOU Cancellation]
    D2c --> D2d[IOU Transfer]

    %% IOU Tracking
    D3 --> D3a[Outstanding IOUs]
    D3a --> D3b[Overdue IOUs]
    D3b --> D3c[IOU Aging]
    D3c --> D3d[IOU Alerts]

    %% IOU Settlement
    D4 --> D4a[Payment Processing]
    D4a --> D4b[Settlement Verification]
    D4b --> D4c[Account Reconciliation]
    D4c --> D4d[Settlement Reports]

    %% Integration Points
    A --> E[Integration Points]
    E --> E1[HR Module Integration]
    E --> E2[Inventory Integration]
    E --> E3[Accounts Integration]
    E --> E4[Security Integration]

    %% Workflow Management
    A --> F[Workflow Management]
    F --> F1[Approval Workflows]
    F --> F2[Notification System]
    F --> F3[Alert Management]
    F --> F4[Status Tracking]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef schedulerClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef gatepassClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef iouClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef integrationClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef workflowClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A startClass
    class B,B1,B2,B3,B4 schedulerClass
    class C,C1,C2,C3,C4 gatepassClass
    class D,D1,D2,D3,D4 iouClass
    class E,E1,E2,E3,E4 integrationClass
    class F,F1,F2,F3,F4 workflowClass
