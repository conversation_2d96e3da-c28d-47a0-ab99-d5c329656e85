graph TD
    A[Supplier Menu] --> B[Page Load & Initialization]
    A --> C[Supplier Operations]
    A --> D[Supplier Management]
    A --> E[Print & Reports]

    %% Page Load & Initialization
    B --> B1[Load Supplier Dashboard]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Setup Navigation Menu]
    B3 --> B4[Load Supplier Grid]
    B4 --> B5[Apply Company Filter]
    B5 --> B6[Display Supplier List]
    B6 --> B7[Setup Action Buttons]
    B7 --> B8[Enable Search Functionality]
    B8 --> B9[Page Ready for User Interaction]

    %% Supplier Operations
    C --> C1[New Supplier Registration]
    C --> C2[Edit Supplier Details]
    C --> C3[Delete Supplier]
    C --> C4[View Supplier Details]

    %% New Supplier Registration
    C1 --> C1a[Navigate to New Supplier Form]
    C1a --> C1b[Load Supplier Registration Form]
    C1b --> C1c[Basic Information Section]
    C1c --> C1d[Supplier Name Entry]
    C1d --> C1e[Contact Information]
    C1e --> C1f[Address Details]
    C1f --> C1g[Business Nature Selection]
    C1g --> C1h[Business Type Selection]
    C1h --> C1i[Service Coverage Selection]
    C1i --> C1j[Financial Information]
    C1j --> C1k[Bank Details]
    C1k --> C1l[Tax Information]
    C1l --> C1m[Rating Parameters]
    C1m --> C1n[Quality Rating]
    C1n --> C1o[Delivery Rating]
    C1o --> C1p[Service Rating]
    C1p --> C1q[Document Attachments]
    C1q --> C1r[Form Validation]
    C1r --> C1s{Validation Passed?}
    C1s -->|No| C1t[Show Validation Errors]
    C1s -->|Yes| C1u[Save Supplier Master]
    
    C1t --> C1c
    C1u --> C1v[Generate Supplier Code]
    C1v --> C1w[Set System Information]
    C1w --> C1x[Set Company ID]
    C1x --> C1y[Set Session Details]
    C1y --> C1z[Set Financial Year]
    C1z --> C1aa[Execute Database Insert]
    C1aa --> C1bb{Insert Successful?}
    C1bb -->|No| C1cc[Database Error Message]
    C1bb -->|Yes| C1dd[Registration Success Message]
    
    C1cc --> C1c
    C1dd --> C1ee[Clear Form]
    C1ee --> C1ff[Refresh Supplier Grid]
    C1ff --> C1gg[Return to Supplier List]

    %% Edit Supplier Details
    C2 --> C2a[Select Supplier for Edit]
    C2a --> C2b[Navigate to Edit Form]
    C2b --> C2c[Load Supplier Data]
    C2c --> C2d[Pre-populate Form Fields]
    C2d --> C2e[Enable Field Modifications]
    C2e --> C2f[Update Basic Information]
    C2f --> C2g[Update Contact Details]
    C2g --> C2h[Update Address Information]
    C2h --> C2i[Update Business Classifications]
    C2i --> C2j[Update Financial Details]
    C2j --> C2k[Update Rating Information]
    C2k --> C2l[Update Document Attachments]
    C2l --> C2m[Form Validation]
    C2m --> C2n{Validation Passed?}
    C2n -->|No| C2o[Show Validation Errors]
    C2n -->|Yes| C2p[Update Supplier Record]
    
    C2o --> C2f
    C2p --> C2q[Set Update Information]
    C2q --> C2r[Set Modified Date/Time]
    C2r --> C2s[Set Modified By User]
    C2s --> C2t[Execute Database Update]
    C2t --> C2u{Update Successful?}
    C2u -->|No| C2v[Update Error Message]
    C2u -->|Yes| C2w[Update Success Message]
    
    C2v --> C2f
    C2w --> C2x[Refresh Supplier Grid]
    C2x --> C2y[Return to Supplier List]

    %% Delete Supplier
    C3 --> C3a[Select Supplier for Delete]
    C3a --> C3b[Navigate to Delete Confirmation]
    C3b --> C3c[Load Supplier Details]
    C3c --> C3d[Display Supplier Information]
    C3d --> C3e[Show Delete Warning]
    C3e --> C3f[Confirm Deletion Dialog]
    C3f --> C3g{Confirm Delete?}
    C3g -->|No| C3h[Cancel Deletion]
    C3g -->|Yes| C3i[Check Dependencies]
    
    C3h --> B6
    C3i --> C3j[Check Purchase Orders]
    C3j --> C3k[Check Rate Sets]
    C3k --> C3l[Check Active Transactions]
    C3l --> C3m{Has Dependencies?}
    C3m -->|Yes| C3n[Cannot Delete - Dependencies Exist]
    C3m -->|No| C3o[Execute Delete Operation]
    
    C3n --> B6
    C3o --> C3p[Delete Supplier Record]
    C3p --> C3q[Delete Related Data]
    C3q --> C3r{Delete Successful?}
    C3r -->|No| C3s[Delete Error Message]
    C3r -->|Yes| C3t[Delete Success Message]
    
    C3s --> B6
    C3t --> C3u[Refresh Supplier Grid]
    C3u --> C3v[Update Supplier Count]
    C3v --> B6

    %% View Supplier Details
    C4 --> C4a[Select Supplier for View]
    C4a --> C4b[Navigate to Details View]
    C4b --> C4c[Load Complete Supplier Data]
    C4c --> C4d[Display Basic Information]
    C4d --> C4e[Display Contact Details]
    C4e --> C4f[Display Address Information]
    C4f --> C4g[Display Business Classifications]
    C4g --> C4h[Display Financial Information]
    C4h --> C4i[Display Rating Information]
    C4i --> C4j[Display Document Attachments]
    C4j --> C4k[Display Transaction History]
    C4k --> C4l[Display Performance Metrics]
    C4l --> C4m[Show Action Buttons]
    C4m --> C4n[Edit Option]
    C4n --> C4o[Delete Option]
    C4o --> C4p[Print Option]

    %% Supplier Management
    D --> D1[Supplier Search & Filter]
    D --> D2[Supplier List Management]
    D --> D3[Bulk Operations]

    %% Supplier Search & Filter
    D1 --> D1a[Search Interface]
    D1a --> D1b[Supplier Name Search]
    D1b --> D1c[Supplier Code Search]
    D1c --> D1d[Business Nature Filter]
    D1d --> D1e[Business Type Filter]
    D1e --> D1f[Service Coverage Filter]
    D1f --> D1g[Rating Filter]
    D1g --> D1h[Status Filter]
    D1h --> D1i[Apply Search Filters]
    D1i --> D1j[Execute Search Query]
    D1j --> D1k[Display Filtered Results]
    D1k --> D1l[Update Result Count]
    D1l --> D1m[Enable Export Options]

    %% Supplier List Management
    D2 --> D2a[Grid Display Options]
    D2a --> D2b[Column Selection]
    D2b --> D2c[Sorting Options]
    D2c --> D2d[Pagination Settings]
    D2d --> D2e[Row Selection]
    D2e --> D2f[Bulk Action Selection]
    D2f --> D2g[Grid Refresh]
    D2g --> D2h[Export Grid Data]

    %% Bulk Operations
    D3 --> D3a[Select Multiple Suppliers]
    D3a --> D3b[Bulk Update Options]
    D3b --> D3c[Bulk Rating Update]
    D3c --> D3d[Bulk Status Change]
    D3d --> D3e[Bulk Export]
    D3e --> D3f[Bulk Print]
    D3f --> D3g[Execute Bulk Operation]
    D3g --> D3h{Operation Successful?}
    D3h -->|No| D3i[Bulk Operation Error]
    D3h -->|Yes| D3j[Bulk Operation Success]
    
    D3i --> D3a
    D3j --> D3k[Refresh Grid]
    D3k --> D3l[Update Status]

    %% Print & Reports
    E --> E1[Print Supplier Details]
    E --> E2[Print All Suppliers]
    E --> E3[Supplier Reports]
    E --> E4[Export Options]

    %% Print Supplier Details
    E1 --> E1a[Select Supplier for Print]
    E1a --> E1b[Load Print Template]
    E1b --> E1c[Format Supplier Data]
    E1c --> E1d[Include Basic Information]
    E1d --> E1e[Include Contact Details]
    E1e --> E1f[Include Financial Information]
    E1f --> E1g[Include Rating Information]
    E1g --> E1h[Generate Print Preview]
    E1h --> E1i[Print Document]
    E1i --> E1j[Save Print Log]

    %% Print All Suppliers
    E2 --> E2a[Load All Suppliers Data]
    E2a --> E2b[Apply Current Filters]
    E2b --> E2c[Format Supplier List]
    E2c --> E2d[Generate Summary Report]
    E2d --> E2e[Include Statistics]
    E2e --> E2f[Generate Print Preview]
    E2f --> E2g[Print Report]
    E2g --> E2h[Save Print Log]

    %% Supplier Reports
    E3 --> E3a[Supplier Performance Report]
    E3a --> E3b[Supplier Rating Report]
    E3b --> E3c[Supplier Analysis Report]
    E3c --> E3d[Supplier Comparison Report]
    E3d --> E3e[Generate Selected Report]
    E3e --> E3f[Export Report]

    %% Export Options
    E4 --> E4a[Excel Export]
    E4a --> E4b[PDF Export]
    E4b --> E4c[CSV Export]
    E4c --> E4d[XML Export]
    E4d --> E4e[Custom Format Export]
    E4e --> E4f[Execute Export]
    E4f --> E4g[Download File]

    %% Error Handling & Validation
    A --> F[Error Handling]
    F --> F1[Form Validation Errors]
    F1 --> F1a[Required Field Validation]
    F1a --> F1b[Data Type Validation]
    F1b --> F1c[Business Rule Validation]
    F1c --> F1d[Duplicate Check Validation]
    
    F --> F2[Database Errors]
    F2 --> F2a[Connection Errors]
    F2a --> F2b[Transaction Errors]
    F2b --> F2c[Constraint Violations]
    
    F --> F3[System Errors]
    F3 --> F3a[Session Timeout]
    F3a --> F3b[Permission Errors]
    F3b --> F3c[Network Errors]

    %% Security Features
    A --> G[Security Features]
    G --> G1[Access Control]
    G1 --> G1a[User Authentication]
    G1a --> G1b[Role-based Permissions]
    G1b --> G1c[Company Data Isolation]
    
    G --> G2[Data Protection]
    G2 --> G2a[Input Sanitization]
    G2a --> G2b[SQL Injection Prevention]
    G2b --> G2c[XSS Protection]
    
    G --> G3[Audit Trail]
    G3 --> G3a[User Action Logging]
    G3a --> G3b[Data Change Tracking]
    G3b --> G3c[System Event Logging]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef operationClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef managementClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef printClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B9 initClass
    class C,C1,C2,C3,C4 operationClass
    class D,D1,D2,D3 managementClass
    class E,E1,E2,E3,E4 printClass
    class C1s,C1bb,C2n,C2u,C3g,C3m,C3r,D3h decisionClass
    class C1t,C1cc,C2o,C2v,C3n,C3s,D3i errorClass
    class C1dd,C2w,C3t,D3j successClass
