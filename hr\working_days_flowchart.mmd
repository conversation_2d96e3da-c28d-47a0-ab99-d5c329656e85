flowchart TD
    A[User Access Working Days Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Working Days Master Page]
    
    D --> E[Initialize Session Context]
    E --> F[CompId, SessionId, FinYearId]
    F --> G[Display Working Days Grid]
    G --> H{User Action}
    
    %% View/Display Operations
    H -->|View Records| I[Query tblHR_WorkingDays]
    I --> J[Filter by CompId and FinYearId]
    J --> K[Display Grid with Pagination]
    K --> L[Show Month, Year, Working Days]
    L --> M[Show Edit/Delete Links]
    M --> H
    
    %% Add New Working Days Record
    H -->|Add New Record| N[Show Add Working Days Form]
    N --> O[User Enters Working Days Details]
    O --> P[Month Selection]
    P --> Q[Year Selection]
    Q --> R[Working Days Input]
    R --> S{Validation Check}
    S -->|Empty Fields| T[Show Required Field Error]
    T --> O
    S -->|Invalid Days| U[Show Days Range Error]
    U --> O
    S -->|Valid Data| V[Check Month-Year Uniqueness]
    V --> W{Month-Year Exists?}
    W -->|Yes| X[Show 'Working Days Already Exists' Alert]
    X --> O
    W -->|No| Y[Calculate Working Days]
    Y --> Z[Auto-calculate from Calendar]
    Z --> AA[Subtract Holidays]
    AA --> BB[Subtract Weekends]
    BB --> CC[Insert into tblHR_WorkingDays]
    CC --> DD[Set SysDate, SysTime, SessionId, CompId, FinYearId]
    DD --> EE{Insert Success?}
    EE -->|Success| FF[Show 'Working Days Added' Message]
    EE -->|Error| GG[Show Error Message]
    FF --> HH[Refresh Grid Display]
    GG --> HH
    HH --> H
    
    %% Edit Working Days
    H -->|Edit Record| II[Enable Edit Mode for Row]
    II --> JJ[User Modifies Working Days]
    JJ --> KK{Update Validation}
    KK -->|Invalid| LL[Show Validation Error]
    LL --> JJ
    KK -->|Valid| MM[Confirm Update Action]
    MM --> NN{User Confirms?}
    NN -->|No| OO[Cancel Edit Mode]
    OO --> H
    NN -->|Yes| PP[Update tblHR_WorkingDays Record]
    PP --> QQ{Update Success?}
    QQ -->|Success| RR[Show 'Record Updated' Message]
    QQ -->|Error| SS[Show Update Error]
    RR --> TT[Refresh Grid Display]
    SS --> TT
    TT --> H
    
    %% Delete Working Days
    H -->|Delete Record| UU[Show Delete Confirmation]
    UU --> VV{User Confirms Delete?}
    VV -->|No| H
    VV -->|Yes| WW[Check Working Days Usage]
    WW --> XX{Working Days in Use?}
    XX -->|Yes| YY[Show 'Cannot Delete - In Use' Error]
    YY --> H
    XX -->|No| ZZ[Delete from tblHR_WorkingDays]
    ZZ --> AAA{Delete Success?}
    AAA -->|Success| BBB[Show 'Record Deleted' Message]
    AAA -->|Error| CCC[Show Delete Error]
    BBB --> DDD[Refresh Grid Display]
    CCC --> DDD
    DDD --> H
    
    %% Auto-Calculate Working Days
    H -->|Auto-Calculate| EEE[Load Auto-Calculate Interface]
    EEE --> FFF[Select Calculation Period]
    FFF --> GGG{Calculation Type}
    GGG -->|Single Month| HHH[Calculate Single Month]
    GGG -->|Financial Year| III[Calculate Full Financial Year]
    GGG -->|Date Range| JJJ[Calculate Date Range]
    
    HHH --> KKK[Get Month Calendar]
    III --> LLL[Get Financial Year Calendar]
    JJJ --> MMM[Get Date Range Calendar]
    
    KKK --> NNN[Count Total Days]
    LLL --> NNN
    MMM --> NNN
    NNN --> OOO[Subtract Holidays]
    OOO --> PPP[Subtract Weekends]
    PPP --> QQQ[Apply Company Rules]
    QQQ --> RRR[Calculate Working Days]
    RRR --> SSS[Save Calculated Results]
    SSS --> TTT[Display Calculation Summary]
    TTT --> H
    
    %% Working Days Configuration
    H -->|Configure Rules| UUU[Load Working Days Configuration]
    UUU --> VVV{Configuration Type}
    VVV -->|Weekend Rules| WWW[Configure Weekend Days]
    VVV -->|Holiday Rules| XXX[Configure Holiday Impact]
    VVV -->|Shift Rules| YYY[Configure Shift Working Days]
    VVV -->|Overtime Rules| ZZZ[Configure Overtime Days]
    
    WWW --> AAAA[Set Weekend Days (Sat/Sun)]
    XXX --> BBBB[Set Holiday Calculation Rules]
    YYY --> CCCC[Set Shift-based Rules]
    ZZZ --> DDDD[Set Overtime Calculation Rules]
    
    AAAA --> EEEE[Save Configuration]
    BBBB --> EEEE
    CCCC --> EEEE
    DDDD --> EEEE
    EEEE --> H
    
    %% Working Days Reports
    H -->|Working Days Reports| FFFF[Working Days Reporting Interface]
    FFFF --> GGGG{Report Type}
    GGGG -->|Monthly Report| HHHH[Monthly Working Days Report]
    GGGG -->|Annual Report| IIII[Annual Working Days Report]
    GGGG -->|Comparison Report| JJJJ[Year-over-Year Comparison]
    GGGG -->|Attendance Impact| KKKK[Attendance Impact Analysis]
    
    HHHH --> LLLL[Generate Monthly Report]
    IIII --> MMMM[Generate Annual Report]
    JJJJ --> NNNN[Generate Comparison Report]
    KKKK --> OOOO[Generate Impact Analysis]
    
    LLLL --> PPPP[Export Working Days Reports]
    MMMM --> PPPP
    NNNN --> PPPP
    OOOO --> PPPP
    PPPP --> H
    
    %% Calendar Integration
    H -->|Calendar View| QQQQ[Load Working Days Calendar]
    QQQQ --> RRRR[Display Monthly Calendar]
    RRRR --> SSSS[Highlight Working Days]
    SSSS --> TTTT[Highlight Holidays]
    TTTT --> UUUU[Highlight Weekends]
    UUUU --> VVVV{Calendar Action}
    
    VVVV -->|Edit Day| WWWW[Edit Day Status]
    VVVV -->|Add Holiday| XXXX[Add Holiday to Calendar]
    VVVV -->|View Details| YYYY[View Day Details]
    VVVV -->|Navigate Month| ZZZZ[Navigate Calendar Months]
    
    WWWW --> AAAAA[Update Day Configuration]
    XXXX --> BBBBB[Add Holiday Record]
    YYYY --> CCCCC[Display Day Information]
    ZZZZ --> DDDDD[Load Different Month]
    
    AAAAA --> EEEEE[Recalculate Working Days]
    BBBBB --> EEEEE
    EEEEE --> RRRR
    CCCCC --> H
    DDDDD --> RRRR
    
    %% Business Rules
    FFFFF[Business Rules] --> GGGGG[Working days must be positive]
    GGGGG --> HHHHH[Working days <= Total days in month]
    HHHHH --> IIIII[Month-Year combination unique]
    IIIII --> JJJJJ[Financial year context required]
    JJJJJ --> KKKKK[Company context required]
    KKKKK --> LLLLL[Holiday integration mandatory]
    LLLLL --> S
    
    %% Django Implementation
    MMMMM[Django Implementation] --> NNNNN[WorkingDays Model]
    NNNNN --> OOOOO[WorkingDaysConfiguration Model]
    OOOOO --> PPPPP[WorkingDaysCalculator Service]
    PPPPP --> QQQQQ[WorkingDaysForm Validation]
    QQQQQ --> RRRRR[Working Days Management Views]
    RRRRR --> SSSSS[Calendar Integration Views]
    SSSSS --> TTTTT[Working Days Reporting Views]
    TTTTT --> UUUUU[SAP Fiori UI Templates]
    UUUUU --> VVVVV[HTMX Dynamic Updates]
    VVVVV --> H
    
    %% Integration Points
    WWWWW[Integration Points] --> XXXXX[Holiday Management]
    XXXXX --> YYYYY[Attendance System]
    YYYYY --> ZZZZZ[Payroll Processing]
    ZZZZZ --> AAAAAA[Leave Management]
    AAAAAA --> BBBBBB[Overtime Calculation]
    BBBBBB --> CCCCCC[Shift Management]
    CCCCCC --> D
    
    %% Calculation Engine
    DDDDDD[Calculation Engine] --> EEEEEE[Calendar Day Counter]
    EEEEEE --> FFFFFF[Holiday Subtractor]
    FFFFFF --> GGGGGG[Weekend Subtractor]
    GGGGGG --> HHHHHH[Company Rule Applicator]
    HHHHHH --> IIIIII[Working Days Computer]
    IIIIII --> Y
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style CC fill:#e8f5e8
    style PP fill:#e8f5e8
    style ZZ fill:#ffebee
    style SSS fill:#e8f5e8
    style EEEE fill:#e8f5e8
    style X fill:#fff3e0
    style T fill:#fff3e0
    style U fill:#fff3e0
    style YY fill:#fff3e0
    style LLLL fill:#fff3e0
    style MMMM fill:#fff3e0
    style NNNN fill:#fff3e0
    style OOOO fill:#fff3e0
    style AAAAA fill:#e8f5e8
    style BBBBB fill:#e8f5e8
    style EEEEE fill:#e8f5e8
    style MMMMM fill:#f1f8e9
    style FFFFF fill:#e3f2fd
    style WWWWW fill:#e0f2f1
    style DDDDDD fill:#f3e5f5
