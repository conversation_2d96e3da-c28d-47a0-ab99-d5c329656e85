flowchart TD
    A[User Access Mobile Bill Management] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Mobile Bill Management Page]
    
    D --> E[Display Mobile Bill Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Bills| G[Query tblHR_MobileBill]
    G --> H[Display Bill Grid with Pagination]
    H --> I[Show Mobile No, Month, Amount, Status]
    I --> J[Show Edit/Delete/Approve Links]
    J --> F
    
    %% Add New Mobile Bill
    F -->|Add New Bill| K[Load Mobile Bill Entry Form]
    K --> L[Mobile Number Selection]
    L --> M[Employee Auto-populate]
    M --> N[Bill Month Selection]
    N --> O[Bill Amount Input]
    O --> P[Tax Amount Input]
    P --> Q[Bill Date Input]
    Q --> R[Bill Description/Notes]
    R --> S[Bill Document Upload]
    S --> T{Validation Check}
    T -->|Empty Fields| U[Show Required Field Error]
    U --> K
    T -->|Invalid Amount| V[Show Amount Validation Error]
    V --> K
    T -->|Duplicate Bill| W[Show Duplicate Bill Error]
    W --> K
    T -->|Valid Data| X[Check Mobile Limit]
    X --> Y{Within Limit?}
    Y -->|No| Z[Show Limit Exceeded Warning]
    Z --> AA{Continue Anyway?}
    AA -->|No| K
    AA -->|Yes| BB[Insert into tblHR_MobileBill]
    Y -->|Yes| BB
    BB --> CC[Set Status to Pending]
    CC --> DD[Generate Bill Entry]
    DD --> EE[Send for Approval]
    EE --> FF[Mobile Bill Entry Complete]
    FF --> F
    
    %% Edit Mobile Bill
    F -->|Edit Bill| GG[Load Bill Edit Form]
    GG --> HH[Check Bill Status]
    HH --> II{Status Check}
    II -->|Approved/Paid| JJ[Show 'Cannot Edit - Processed' Error]
    JJ --> F
    II -->|Pending| KK[Enable Bill Editing]
    KK --> LL[User Modifies Bill Details]
    LL --> MM{Update Validation}
    MM -->|Invalid| NN[Show Validation Error]
    NN --> LL
    MM -->|Valid| OO[Update tblHR_MobileBill]
    OO --> PP[Reset Approval Status]
    PP --> QQ[Update Bill Record]
    QQ --> RR[Bill Update Complete]
    RR --> F
    
    %% Mobile Bill Approval
    F -->|Approve Bills| SS[Load Bill Approval Interface]
    SS --> TT[Display Pending Bills]
    TT --> UU[Show Bill Details]
    UU --> VV{Approval Action}
    VV -->|Approve| WW[Approve Mobile Bill]
    VV -->|Reject| XX[Reject Mobile Bill]
    VV -->|Request Info| YY[Request Additional Information]
    
    WW --> ZZ[Update Status to Approved]
    XX --> AAA[Update Status to Rejected]
    YY --> BBB[Update Status to Info Required]
    
    ZZ --> CCC[Set Approved By & Date]
    AAA --> DDD[Set Rejection Reason]
    BBB --> EEE[Set Info Request Details]
    
    CCC --> FFF[Generate Approval Document]
    DDD --> GGG[Send Rejection Notification]
    EEE --> HHH[Send Info Request]
    
    FFF --> III[Send Approval Notification]
    GGG --> F
    HHH --> F
    III --> F
    
    %% Mobile Bill Payment Processing
    F -->|Process Payment| JJJ[Load Payment Processing]
    JJJ --> KKK[Display Approved Bills]
    KKK --> LLL[Select Bills for Payment]
    LLL --> MMM[Verify Payment Details]
    MMM --> NNN[Generate Payment Voucher]
    NNN --> OOO[Process Payment]
    OOO --> PPP[Update Payment Status]
    PPP --> QQQ[Generate Payment Report]
    QQQ --> RRR[Send Payment Confirmation]
    RRR --> F
    
    %% Mobile Bill Reports
    F -->|Bill Reports| SSS[Mobile Bill Reporting]
    SSS --> TTT{Report Type}
    TTT -->|Monthly Report| UUU[Monthly Bill Report]
    TTT -->|Employee Report| VVV[Employee-wise Bill Report]
    TTT -->|Department Report| WWW[Department-wise Bill Report]
    TTT -->|Limit Analysis| XXX[Limit Analysis Report]
    TTT -->|Payment Report| YYY[Payment Status Report]
    
    UUU --> ZZZ[Generate Monthly Report]
    VVV --> AAAA[Generate Employee Report]
    WWW --> BBBB[Generate Department Report]
    XXX --> CCCC[Generate Limit Analysis]
    YYY --> DDDD[Generate Payment Report]
    
    ZZZ --> EEEE[Export Bill Reports]
    AAAA --> EEEE
    BBBB --> EEEE
    CCCC --> EEEE
    DDDD --> EEEE
    EEEE --> F
    
    %% Mobile Usage Analytics
    F -->|Usage Analytics| FFFF[Mobile Usage Analytics]
    FFFF --> GGGG{Analytics Type}
    GGGG -->|Usage Trends| HHHH[Usage Trend Analysis]
    GGGG -->|Cost Analysis| IIII[Cost Analysis]
    GGGG -->|Limit Utilization| JJJJ[Limit Utilization Analysis]
    GGGG -->|Comparative Analysis| KKKK[Comparative Usage Analysis]
    
    HHHH --> LLLL[Generate Trend Charts]
    IIII --> MMMM[Generate Cost Charts]
    JJJJ --> NNNN[Generate Utilization Charts]
    KKKK --> OOOO[Generate Comparison Charts]
    
    LLLL --> PPPP[Display Analytics Dashboard]
    MMMM --> PPPP
    NNNN --> PPPP
    OOOO --> PPPP
    PPPP --> F
    
    %% Mobile Bill Reconciliation
    F -->|Reconciliation| QQQQ[Mobile Bill Reconciliation]
    QQQQ --> RRRR[Load Reconciliation Interface]
    RRRR --> SSSS[Import Operator Bills]
    SSSS --> TTTT[Match with Internal Records]
    TTTT --> UUUU[Identify Discrepancies]
    UUUU --> VVVV{Discrepancy Found?}
    VVVV -->|Yes| WWWW[Flag Discrepancies]
    VVVV -->|No| XXXX[Mark as Reconciled]
    
    WWWW --> YYYY[Generate Discrepancy Report]
    XXXX --> ZZZZ[Update Reconciliation Status]
    
    YYYY --> AAAAA[Send Discrepancy Alert]
    ZZZZ --> BBBBB[Complete Reconciliation]
    
    AAAAA --> F
    BBBBB --> F
    
    %% Mobile Bill Bulk Operations
    F -->|Bulk Operations| CCCCC[Mobile Bill Bulk Operations]
    CCCCC --> DDDDD{Bulk Action}
    DDDDD -->|Bulk Import| EEEEE[Bulk Bill Import]
    DDDDD -->|Bulk Approval| FFFFF[Bulk Bill Approval]
    DDDDD -->|Bulk Payment| GGGGG[Bulk Payment Processing]
    
    EEEEE --> HHHHH[Upload Bill Data File]
    FFFFF --> IIIII[Select Bills for Approval]
    GGGGG --> JJJJJ[Select Bills for Payment]
    
    HHHHH --> KKKKK[Process Import Data]
    IIIII --> LLLLL[Process Bulk Approvals]
    JJJJJ --> MMMMM[Process Bulk Payments]
    
    KKKKK --> NNNNN[Import Validation & Processing]
    LLLLL --> OOOOO[Approval Processing]
    MMMMM --> PPPPP[Payment Processing]
    
    NNNNN --> QQQQQ[Show Import Results]
    OOOOO --> RRRRR[Show Approval Results]
    PPPPP --> SSSSS[Show Payment Results]
    
    QQQQQ --> F
    RRRRR --> F
    SSSSS --> F
    
    %% Business Rules
    TTTTT[Business Rules] --> UUUUU[Mobile number must exist in master]
    UUUUU --> VVVVV[Bill amount must be positive]
    VVVVV --> WWWWW[Bill month must be valid]
    WWWWW --> XXXXX[Duplicate bill prevention]
    XXXXX --> YYYYY[Limit validation]
    YYYYY --> ZZZZZ[Approval workflow mandatory]
    ZZZZZ --> T
    
    %% Django Implementation
    AAAAAA[Django Implementation] --> BBBBBB[MobileBill Model]
    BBBBBB --> CCCCCC[MobileBillApproval Model]
    CCCCCC --> DDDDDD[MobileBillPayment Model]
    DDDDDD --> EEEEEE[MobileBillForm Validation]
    EEEEEE --> FFFFFF[Mobile Bill Management Views]
    FFFFFF --> GGGGGG[Bill Approval Views]
    GGGGGG --> HHHHHH[Payment Processing Views]
    HHHHHH --> IIIIII[SAP Fiori UI Templates]
    IIIIII --> JJJJJJ[HTMX Dynamic Updates]
    JJJJJJ --> F
    
    %% Integration Points
    KKKKKK[Integration Points] --> LLLLLL[Corporate Mobile Master]
    LLLLLL --> MMMMMM[Employee Management]
    MMMMMM --> NNNNNN[Approval Workflow]
    NNNNNN --> OOOOOO[Accounts Integration]
    OOOOOO --> PPPPPP[Payment Processing]
    PPPPPP --> QQQQQQ[Expense Management]
    QQQQQQ --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style BB fill:#e8f5e8
    style OO fill:#e8f5e8
    style WW fill:#e8f5e8
    style XX fill:#ffebee
    style YY fill:#fff3e0
    style OOO fill:#e8f5e8
    style U fill:#fff3e0
    style V fill:#fff3e0
    style W fill:#fff3e0
    style Z fill:#fff3e0
    style JJ fill:#fff3e0
    style ZZZ fill:#fff3e0
    style AAAA fill:#fff3e0
    style BBBB fill:#fff3e0
    style CCCC fill:#fff3e0
    style DDDD fill:#fff3e0
    style LLLL fill:#e8f5e8
    style MMMM fill:#e8f5e8
    style NNNN fill:#e8f5e8
    style OOOO fill:#e8f5e8
    style WWWW fill:#fff3e0
    style XXXX fill:#e8f5e8
    style KKKKK fill:#e8f5e8
    style LLLLL fill:#e8f5e8
    style MMMMM fill:#e8f5e8
    style AAAAAA fill:#f1f8e9
    style TTTTT fill:#e3f2fd
    style KKKKKK fill:#e0f2f1
