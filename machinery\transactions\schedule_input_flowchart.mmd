graph TD
    A[Schedule Input - Job Scheduling] --> B[Work Order Selection]
    A --> C[Item Scheduling]
    A --> D[Schedule Management]

    %% Work Order Selection Flow
    B --> B1[Load Work Orders]
    B1 --> B2[Apply Company Filter]
    B2 --> B3[Apply Financial Year Filter]
    B3 --> B4[Search Functionality]
    
    B4 --> B5[Search Type Selection]
    B5 --> B6{Search Type}
    B6 -->|Customer| B7[Customer Name Search]
    B6 -->|Work Order| B8[Work Order Number Search]
    
    B7 --> B9[Find Customers by Name]
    B8 --> B10[Find Work Orders by Number]
    B9 --> B11[Get Customer Work Orders]
    B10 --> B12[Filter Work Orders]
    
    B11 --> B13[Enhance with Customer Info]
    B12 --> B13
    B13 --> B14[Display Work Order List]
    B14 --> B15{Work Orders Found?}
    B15 -->|No| B16[No Work Orders Message]
    B15 -->|Yes| B17[Select Work Order]
    
    B17 --> B18{Work Order Selected?}
    B18 -->|Yes| B19[Load Work Order Details]
    B18 -->|No| B20[Stay on Selection Page]
    
    B19 --> B21[Get Customer Information]
    B21 --> B22[Load BOM Items]
    B22 --> B23[Display Work Order Summary]
    B23 --> B24[Navigate to Item Scheduling]
    
    B24 --> C1[Item Selection for Scheduling]

    %% Item Scheduling Flow
    C1 --> C2[Load Work Order Items]
    C2 --> C3[Display BOM Items List]
    C3 --> C4[Select Item for Scheduling]
    C4 --> C5{Item Selected?}
    C5 -->|Yes| C6[Navigate to Scheduling Details]
    C5 -->|No| C7[Stay on Item Selection]
    
    C6 --> C8[Clear Temp Tables]
    C8 --> C9[Load Item Information]
    C9 --> C10[Get BOM Quantity]
    C10 --> C11[Calculate Total Quantity]
    C11 --> C12[Scheduling Form]
    
    C12 --> C13[Machine Selection]
    C13 --> C14[Load Available Machines]
    C14 --> C15[Filter Machines with Processes]
    C15 --> C16[Display Machine Options]
    C16 --> C17[Select Machine]
    C17 --> C18{Machine Selected?}
    C18 -->|Yes| C19[Load Machine Processes]
    C18 -->|No| C20[Machine Selection Required]
    
    C19 --> C21[Process Selection]
    C21 --> C22[Display Available Processes]
    C22 --> C23[Select Process]
    C23 --> C24{Process Selected?}
    C24 -->|Yes| C25[Batch Configuration]
    C24 -->|No| C26[Process Selection Required]
    
    C25 --> C27[Batch Number Selection]
    C27 --> C28[Load Work Order Batches]
    C28 --> C29[Select Batch]
    C29 --> C30[Date & Time Scheduling]
    
    C30 --> C31[From Date Selection]
    C31 --> C32[To Date Selection]
    C32 --> C33[From Time Entry]
    C33 --> C34[To Time Entry]
    C34 --> C35[Quantity Planning]
    
    C35 --> C36[Enter Scheduled Quantity]
    C36 --> C37[UOM Verification]
    C37 --> C38[Operator Assignment]
    C38 --> C39[Select Operator]
    C39 --> C40[Incharge Assignment]
    C40 --> C41[Select Incharge]
    C41 --> C42[Shift Planning]
    C42 --> C43[Select Shift]
    
    C43 --> C44[Schedule Validation]
    C44 --> C45{Validation Passed?}
    C45 -->|No| C46[Show Validation Errors]
    C45 -->|Yes| C47[Add to Schedule]
    
    C46 --> C12
    
    C47 --> C48[Add to Temp Table]
    C48 --> C49[Display Current Schedule]
    C49 --> C50{More Items to Schedule?}
    C50 -->|Yes| C51[Add Another Item]
    C50 -->|No| C52[Review Schedule]
    
    C51 --> C12
    
    C52 --> C53[Schedule Summary]
    C53 --> C54[Validate Complete Schedule]
    C54 --> C55{Schedule Valid?}
    C55 -->|No| C56[Show Schedule Errors]
    C55 -->|Yes| C57[Submit Schedule]
    
    C56 --> C49
    
    C57 --> C58[Save Job Schedule Master]
    C58 --> C59[Generate Job Number]
    C59 --> C60[Save Schedule Details]
    C60 --> C61[Loop Through Temp Records]
    C61 --> C62[Create Schedule Detail Record]
    C62 --> C63{More Details?}
    C63 -->|Yes| C61
    C63 -->|No| C64[Clear Temp Tables]
    
    C64 --> C65[Generate Schedule Reports]
    C65 --> C66[Update Work Order Status]
    C66 --> C67[Success Message]
    C67 --> C68[Redirect to Schedule List]

    %% Split Scheduling Flow (Alternative Path)
    C4 --> C69[Split Scheduling Option]
    C69 --> C70{Use Split Scheduling?}
    C70 -->|Yes| C71[BOM Split View]
    C70 -->|No| C6
    
    C71 --> C72[Load BOM Components]
    C72 --> C73[Get Child Components]
    C73 --> C74[Calculate Component Quantities]
    C74 --> C75[Display Component List]
    C75 --> C76[Select Component for Scheduling]
    C76 --> C77{Component Selected?}
    C77 -->|Yes| C78[Component Scheduling Details]
    C77 -->|No| C79[Stay on Component Selection]
    
    C78 --> C80[Calculate BOM Tree Quantity]
    C80 --> C81[Apply Quantity Multipliers]
    C81 --> C82[Component Machine Selection]
    C82 --> C83[Component Process Selection]
    C83 --> C84[Component Schedule Entry]
    C84 --> C85[Add Component to Schedule]
    C85 --> C86{More Components?}
    C86 -->|Yes| C76
    C86 -->|No| C52

    %% Schedule Management Flow
    D --> D1[Schedule List View]
    D1 --> D2[Load Job Schedules]
    D2 --> D3[Apply Company Filter]
    D3 --> D4[Apply Financial Year Filter]
    D4 --> D5[Search Functionality]
    D5 --> D6[Display Schedule List]
    
    D6 --> D7[Schedule Actions]
    D7 --> D8[View Schedule Details]
    D7 --> D9[Edit Schedule]
    D7 --> D10[Delete Schedule]
    D7 --> D11[Print Schedule]
    D7 --> D12[Release Schedule]
    
    %% View Schedule Details
    D8 --> D13[Load Schedule Record]
    D13 --> D14[Display Schedule Master]
    D14 --> D15[Show Work Order Info]
    D15 --> D16[Show Customer Details]
    D16 --> D17[Load Schedule Details]
    D17 --> D18[Display Scheduled Items]
    D18 --> D19[Show Machine Assignments]
    D19 --> D20[Show Time Schedules]
    D20 --> D21[Show Operator Assignments]
    
    %% Edit Schedule
    D9 --> D22[Load Schedule for Edit]
    D22 --> D23[Pre-populate Form]
    D23 --> D24[Allow Modifications]
    D24 --> D25[Update Schedule Details]
    D25 --> D26[Validate Changes]
    D26 --> D27{Changes Valid?}
    D27 -->|No| D28[Show Validation Errors]
    D27 -->|Yes| D29[Save Changes]
    
    D28 --> D24
    D29 --> D30[Update System Date/Time]
    D30 --> D31[Success Message]
    
    %% Delete Schedule
    D10 --> D32[Confirm Deletion]
    D32 --> D33{Confirm Delete?}
    D33 -->|No| D6
    D33 -->|Yes| D34[Check Schedule Status]
    D34 --> D35{Schedule Released?}
    D35 -->|Yes| D36[Cannot Delete Released]
    D35 -->|No| D37[Delete Schedule Details]
    
    D36 --> D6
    D37 --> D38[Delete Schedule Master]
    D38 --> D39[Success Message]
    D39 --> D6
    
    %% Release Schedule
    D12 --> D40[Check Schedule Completeness]
    D40 --> D41{Schedule Complete?}
    D41 -->|No| D42[Show Incomplete Items]
    D41 -->|Yes| D43[Release Schedule]
    
    D42 --> D6
    D43 --> D44[Update Release Status]
    D44 --> D45[Set Release Date/Time]
    D45 --> D46[Set Released By]
    D46 --> D47[Generate Production Orders]
    D47 --> D48[Notify Production Team]
    D48 --> D49[Success Message]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef warningClass fill:#fff8e1,stroke:#f9a825,stroke-width:2px

    class A startClass
    class B6,B15,B18,C5,C18,C24,C45,C50,C55,C63,C70,C77,C86,D27,D33,D35,D41 decisionClass
    class B16,C20,C26,C46,C56,D28,D36,D42 errorClass
    class C67,D31,D39,D49 successClass
    class C58,C60,C64,D13,D22,D37,D43 dataClass
