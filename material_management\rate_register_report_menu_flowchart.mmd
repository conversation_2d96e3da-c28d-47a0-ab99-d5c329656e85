graph TD
    A[Rate Register Report Menu] --> B[Page Load & Initialization]
    A --> C[Report Parameters]
    A --> D[Report Generation]
    A --> E[Report Output]

    %% Page Load & Initialization
    B --> B1[Load Rate Register Report Page]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Setup Report Interface]
    B3 --> B4[Load Filter Controls]
    B4 --> B5[Load Item Categories]
    B5 --> B6[Load Supplier List]
    B6 --> B7[Setup Date Controls]
    B7 --> B8[Initialize Report Grid]
    B8 --> B9[Page Ready for User Interaction]

    %% Report Parameters
    C --> C1[Filter Selection]
    C --> C2[Date Range Selection]
    C --> C3[Item Selection]
    C --> C4[Supplier Selection]
    C --> C5[Report Options]

    %% Filter Selection
    C1 --> C1a[Report Type Selection]
    C1a --> C1b{Report Type}
    C1b -->|All Items| C1c[All Items Rate Register]
    C1b -->|Single Item| C1d[Single Item Rate Register]
    C1b -->|Category Wise| C1e[Category Wise Rate Register]
    C1b -->|Supplier Wise| C1f[Supplier Wise Rate Register]
    
    C1c --> C1g[Load All Items Configuration]
    C1d --> C1h[Item Selection Interface]
    C1e --> C1i[Category Selection Interface]
    C1f --> C1j[Supplier Selection Interface]
    
    C1g --> C2
    C1h --> C1k[Browse Item Master]
    C1i --> C1l[Browse Category Master]
    C1j --> C1m[Browse Supplier Master]
    
    C1k --> C1n[Select Specific Item]
    C1l --> C1o[Select Item Category]
    C1m --> C1p[Select Supplier]
    
    C1n --> C1q{Item Selected?}
    C1o --> C1r{Category Selected?}
    C1p --> C1s{Supplier Selected?}
    
    C1q -->|No| C1t[Item Selection Required]
    C1q -->|Yes| C2
    C1r -->|No| C1u[Category Selection Required]
    C1r -->|Yes| C2
    C1s -->|No| C1v[Supplier Selection Required]
    C1s -->|Yes| C2
    
    C1t --> C1k
    C1u --> C1l
    C1v --> C1m

    %% Date Range Selection
    C2 --> C2a[Date Range Interface]
    C2a --> C2b[From Date Selection]
    C2b --> C2c[To Date Selection]
    C2c --> C2d[Date Validation]
    C2d --> C2e{Date Range Valid?}
    C2e -->|No| C2f[Invalid Date Range Error]
    C2e -->|Yes| C2g[Date Range Accepted]
    
    C2f --> C2b
    C2g --> C3

    %% Item Selection (Additional Filters)
    C3 --> C3a[Item Filter Options]
    C3a --> C3b[Item Code Filter]
    C3b --> C3c[Item Description Filter]
    C3c --> C3d[Item Category Filter]
    C3d --> C3e[Item Subcategory Filter]
    C3e --> C3f[UOM Filter]
    C3f --> C3g[Apply Item Filters]
    C3g --> C4

    %% Supplier Selection (Additional Filters)
    C4 --> C4a[Supplier Filter Options]
    C4a --> C4b[Supplier Code Filter]
    C4b --> C4c[Supplier Name Filter]
    C4c --> C4d[Business Nature Filter]
    C4d --> C4e[Business Type Filter]
    C4e --> C4f[Service Coverage Filter]
    C4f --> C4g[Apply Supplier Filters]
    C4g --> C5

    %% Report Options
    C5 --> C5a[Report Format Selection]
    C5a --> C5b{Output Format}
    C5b -->|Grid View| C5c[Grid Display Options]
    C5b -->|Print Preview| C5d[Print Preview Options]
    C5b -->|PDF Export| C5e[PDF Export Options]
    C5b -->|Excel Export| C5f[Excel Export Options]
    
    C5c --> C5g[Column Selection]
    C5d --> C5h[Print Layout Selection]
    C5e --> C5i[PDF Format Options]
    C5f --> C5j[Excel Format Options]
    
    C5g --> C5k[Sorting Options]
    C5h --> C5l[Page Setup Options]
    C5i --> C5m[PDF Security Options]
    C5j --> C5n[Excel Sheet Options]
    
    C5k --> C5o[Grouping Options]
    C5l --> C5p[Header/Footer Options]
    C5m --> C5q[Watermark Options]
    C5n --> C5r[Data Format Options]
    
    C5o --> D
    C5p --> D
    C5q --> D
    C5r --> D

    %% Report Generation
    D --> D1[Parameter Validation]
    D --> D2[Data Retrieval]
    D --> D3[Data Processing]
    D --> D4[Report Formatting]

    %% Parameter Validation
    D1 --> D1a[Validate Required Parameters]
    D1a --> D1b[Check Date Range]
    D1b --> D1c[Check Item Selection]
    D1c --> D1d[Check Supplier Selection]
    D1d --> D1e[Check Report Options]
    D1e --> D1f{All Parameters Valid?}
    D1f -->|No| D1g[Show Parameter Errors]
    D1f -->|Yes| D2
    
    D1g --> C

    %% Data Retrieval
    D2 --> D2a[Build Query Parameters]
    D2a --> D2b[Apply Company Filter]
    D2b --> D2c[Apply Financial Year Filter]
    D2c --> D2d[Apply Date Range Filter]
    D2d --> D2e[Apply Item Filters]
    D2e --> D2f[Apply Supplier Filters]
    D2f --> D2g[Execute Rate Query]
    D2g --> D2h[Retrieve Rate History]
    D2h --> D2i[Get Current Rates]
    D2i --> D2j[Get Rate Changes]
    D2j --> D2k[Get Lock Status]
    D2k --> D2l{Data Retrieved?}
    D2l -->|No| D2m[No Data Found Message]
    D2l -->|Yes| D3
    
    D2m --> C

    %% Data Processing
    D3 --> D3a[Process Rate Data]
    D3a --> D3b[Calculate Rate Variations]
    D3b --> D3c[Identify Rate Trends]
    D3c --> D3d[Group by Categories]
    D3d --> D3e[Sort by Parameters]
    D3e --> D3f[Apply Grouping Logic]
    D3f --> D3g[Calculate Summaries]
    D3g --> D3h[Generate Statistics]
    D3h --> D3i[Format Data for Display]
    D3i --> D4

    %% Report Formatting
    D4 --> D4a[Apply Report Template]
    D4a --> D4b[Format Headers]
    D4b --> D4c[Format Data Columns]
    D4c --> D4d[Apply Number Formatting]
    D4d --> D4e[Apply Date Formatting]
    D4e --> D4f[Add Calculated Fields]
    D4f --> D4g[Add Summary Rows]
    D4g --> D4h[Add Report Footer]
    D4h --> D4i[Apply Styling]
    D4i --> E

    %% Report Output
    E --> E1[Display Report]
    E --> E2[Export Options]
    E --> E3[Print Options]
    E --> E4[Save Options]

    %% Display Report
    E1 --> E1a[Grid Display]
    E1a --> E1b[Load Report Data]
    E1b --> E1c[Apply Pagination]
    E1c --> E1d[Enable Sorting]
    E1d --> E1e[Enable Filtering]
    E1e --> E1f[Enable Column Resize]
    E1f --> E1g[Enable Row Selection]
    E1g --> E1h[Display Report Grid]
    E1h --> E1i[Show Record Count]
    E1i --> E1j[Enable Drill-down]
    E1j --> E1k[Show Rate Details]
    E1k --> E1l[Show Rate History]
    E1l --> E1m[Show Supplier Details]

    %% Export Options
    E2 --> E2a[Excel Export]
    E2a --> E2b[PDF Export]
    E2b --> E2c[CSV Export]
    E2c --> E2d[XML Export]
    
    E2a --> E2e[Generate Excel File]
    E2b --> E2f[Generate PDF File]
    E2c --> E2g[Generate CSV File]
    E2d --> E2h[Generate XML File]
    
    E2e --> E2i[Download Excel File]
    E2f --> E2j[Download PDF File]
    E2g --> E2k[Download CSV File]
    E2h --> E2l[Download XML File]

    %% Print Options
    E3 --> E3a[Print Preview]
    E3a --> E3b[Page Setup]
    E3b --> E3c[Print Configuration]
    E3c --> E3d[Header/Footer Setup]
    E3d --> E3e[Margin Settings]
    E3e --> E3f[Orientation Settings]
    E3f --> E3g[Paper Size Settings]
    E3g --> E3h[Generate Print Preview]
    E3h --> E3i[Print Document]
    E3i --> E3j[Save Print Settings]

    %% Save Options
    E4 --> E4a[Save Report Configuration]
    E4a --> E4b[Save Filter Settings]
    E4b --> E4c[Save Layout Settings]
    E4c --> E4d[Save User Preferences]
    E4d --> E4e[Create Report Template]
    E4e --> E4f[Schedule Report]
    E4f --> E4g[Email Report]
    E4g --> E4h[Archive Report]

    %% Advanced Features
    A --> F[Advanced Features]
    F --> F1[Rate Comparison]
    F --> F2[Rate Analysis]
    F --> F3[Rate Trends]
    F --> F4[Rate Alerts]

    %% Rate Comparison
    F1 --> F1a[Multi-Supplier Comparison]
    F1a --> F1b[Historical Comparison]
    F1b --> F1c[Market Rate Comparison]
    F1c --> F1d[Variance Analysis]
    F1d --> F1e[Best Rate Identification]

    %% Rate Analysis
    F2 --> F2a[Price Trend Analysis]
    F2a --> F2b[Seasonal Analysis]
    F2b --> F2c[Supplier Performance Analysis]
    F2c --> F2d[Cost Saving Analysis]
    F2d --> F2e[Negotiation Analysis]

    %% Rate Trends
    F3 --> F3a[Upward Trend Identification]
    F3a --> F3b[Downward Trend Identification]
    F3b --> F3c[Stable Rate Identification]
    F3c --> F3d[Volatile Rate Identification]
    F3d --> F3e[Trend Forecasting]

    %% Rate Alerts
    F4 --> F4a[Rate Increase Alerts]
    F4a --> F4b[Rate Decrease Alerts]
    F4b --> F4c[Rate Lock Alerts]
    F4c --> F4d[Rate Expiry Alerts]
    F4d --> F4e[Competitive Rate Alerts]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef parameterClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef generationClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef outputClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B9 initClass
    class C,C1,C2,C3,C4,C5 parameterClass
    class D,D1,D2,D3,D4 generationClass
    class E,E1,E2,E3,E4 outputClass
    class C1b,C1q,C1r,C1s,C2e,C5b,D1f,D2l decisionClass
    class C1t,C1u,C1v,C2f,D1g,D2m errorClass
    class C2g,E1h,E2i,E3i successClass
