flowchart TD
    A[User Access Tour Intimation] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Tour Intimation Management]
    
    D --> E[Display Tour Intimation Grid]
    E --> F{User Action}
    
    %% View Tour Intimations
    F -->|View Tours| G[Query tblHR_TourIntimation]
    G --> H[Display Tour Grid with Pagination]
    H --> I[Show Employee, Destination, Dates, Status]
    I --> J[Show Edit/Delete/Approve Links]
    J --> F
    
    %% Create New Tour Intimation
    F -->|Create Tour| K[Load Tour Intimation Form]
    K --> L[Employee Information Section]
    L --> M[Employee Selection/Auto-fill]
    M --> N[Department Auto-populate]
    N --> O[Designation Auto-populate]
    O --> P[Tour Details Section]
    P --> Q[Tour Purpose Input]
    Q --> R[Destination Details]
    R --> S[From Date Selection]
    S --> T[To Date Selection]
    T --> U[Duration Calculation]
    U --> V[Travel Mode Selection]
    V --> W[Accommodation Details]
    W --> X[Contact Information]
    X --> Y[Tour Budget Section]
    Y --> Z[Estimated Travel Cost]
    Z --> AA[Accommodation Cost]
    AA --> BB[Daily Allowance]
    BB --> CC[Other Expenses]
    CC --> DD[Total Budget Calculation]
    DD --> EE[Advance Required]
    EE --> FF{Validation Check}
    FF -->|Invalid Dates| GG[Show Date Validation Error]
    GG --> K
    FF -->|Invalid Budget| HH[Show Budget Validation Error]
    HH --> K
    FF -->|Valid Data| II[Insert into tblHR_TourIntimation]
    II --> JJ[Set Status to Pending]
    JJ --> KK[Generate Tour ID]
    KK --> LL[Send for Approval]
    LL --> MM[Tour Intimation Created]
    MM --> F
    
    %% Edit Tour Intimation
    F -->|Edit Tour| NN[Load Tour for Editing]
    NN --> OO[Check Tour Status]
    OO --> PP{Status Check}
    PP -->|Approved/Completed| QQ[Show 'Cannot Edit - Processed' Error]
    QQ --> F
    PP -->|Pending/Draft| RR[Enable Tour Editing]
    RR --> SS[User Modifies Tour Details]
    SS --> TT{Update Validation}
    TT -->|Invalid| UU[Show Validation Error]
    UU --> SS
    TT -->|Valid| VV[Update tblHR_TourIntimation]
    VV --> WW[Reset Approval Status if needed]
    WW --> XX[Tour Update Complete]
    XX --> F
    
    %% Tour Approval Workflow
    F -->|Approve Tours| YY[Load Tour Approval Interface]
    YY --> ZZ[Display Pending Tours]
    ZZ --> AAA[Show Tour Details for Review]
    AAA --> BBB{Approval Decision}
    BBB -->|Approve| CCC[Approve Tour]
    BBB -->|Reject| DDD[Reject Tour]
    BBB -->|Request Changes| EEE[Request Tour Modifications]
    
    CCC --> FFF[Update Status to Approved]
    DDD --> GGG[Update Status to Rejected]
    EEE --> HHH[Update Status to Revision Required]
    
    FFF --> III[Set Approved By & Date]
    GGG --> JJJ[Set Rejection Reason]
    HHH --> KKK[Set Revision Comments]
    
    III --> LLL[Generate Approval Document]
    JJJ --> MMM[Send Rejection Notification]
    KKK --> NNN[Send Revision Request]
    
    LLL --> OOO[Send Approval Notification]
    MMM --> F
    NNN --> F
    OOO --> F
    
    %% Tour Expense Management
    F -->|Manage Expenses| PPP[Tour Expense Management]
    PPP --> QQQ[Select Approved Tour]
    QQQ --> RRR[Load Expense Entry Form]
    RRR --> SSS[Actual Travel Expenses]
    SSS --> TTT[Accommodation Expenses]
    TTT --> UUU[Food & Miscellaneous]
    UUU --> VVV[Receipt Upload]
    VVV --> WWW[Expense Validation]
    WWW --> XXX{Expense Check}
    XXX -->|Over Budget| YYY[Show Budget Exceeded Warning]
    YYY --> ZZZ{Continue?}
    ZZZ -->|No| RRR
    ZZZ -->|Yes| AAAA[Save Expense Details]
    XXX -->|Within Budget| AAAA
    AAAA --> BBBB[Calculate Reimbursement]
    BBBB --> CCCC[Generate Expense Report]
    CCCC --> DDDD[Send for Expense Approval]
    DDDD --> F
    
    %% Tour Reports
    F -->|Tour Reports| EEEE[Tour Reporting Interface]
    EEEE --> FFFF{Report Type}
    FFFF -->|Tour Summary| GGGG[Tour Summary Report]
    FFFF -->|Employee Tours| HHHH[Employee-wise Tour Report]
    FFFF -->|Department Tours| IIII[Department-wise Tour Report]
    FFFF -->|Budget Analysis| JJJJ[Tour Budget Analysis]
    FFFF -->|Expense Report| KKKK[Tour Expense Report]
    
    GGGG --> LLLL[Generate Summary Report]
    HHHH --> MMMM[Generate Employee Report]
    IIII --> NNNN[Generate Department Report]
    JJJJ --> OOOO[Generate Budget Analysis]
    KKKK --> PPPP[Generate Expense Report]
    
    LLLL --> QQQQ[Export Tour Reports]
    MMMM --> QQQQ
    NNNN --> QQQQ
    OOOO --> QQQQ
    PPPP --> QQQQ
    QQQQ --> F
    
    %% Tour Status Tracking
    F -->|Track Status| RRRR[Tour Status Tracking]
    RRRR --> SSSS[Display Tour Timeline]
    SSSS --> TTTT[Show Status History]
    TTTT --> UUUU{Status Actions}
    UUUU -->|Mark Started| VVVV[Mark Tour as Started]
    UUUU -->|Mark Completed| WWWW[Mark Tour as Completed]
    UUUU -->|Mark Cancelled| XXXX[Mark Tour as Cancelled]
    UUUU -->|Extend Tour| YYYY[Extend Tour Duration]
    
    VVVV --> ZZZZ[Update Status to In Progress]
    WWWW --> AAAAA[Update Status to Completed]
    XXXX --> BBBBB[Update Status to Cancelled]
    YYYY --> CCCCC[Update Tour End Date]
    
    ZZZZ --> DDDDD[Log Status Change]
    AAAAA --> DDDDD
    BBBBB --> DDDDD
    CCCCC --> DDDDD
    DDDDD --> EEEEE[Send Status Notification]
    EEEEE --> F
    
    %% Tour Calendar Management
    F -->|Tour Calendar| FFFFF[Tour Calendar View]
    FFFFF --> GGGGG[Display Calendar Interface]
    GGGGG --> HHHHH[Show Monthly Tour Calendar]
    HHHHH --> IIIII[Highlight Tour Dates]
    IIIII --> JJJJJ{Calendar Action}
    JJJJJ -->|View Tour| KKKKK[View Tour Details]
    JJJJJ -->|Add Tour| LLLLL[Quick Add Tour]
    JJJJJ -->|Edit Tour| MMMMM[Quick Edit Tour]
    JJJJJ -->|Navigate| NNNNN[Navigate Calendar Months]
    
    KKKKK --> F
    LLLLL --> OOOOO[Quick Tour Creation Form]
    MMMMM --> PPPPP[Quick Tour Edit Form]
    NNNNN --> HHHHH
    OOOOO --> F
    PPPPP --> F
    
    %% Tour Templates
    F -->|Tour Templates| QQQQQ[Tour Template Management]
    QQQQQ --> RRRRR{Template Action}
    RRRRR -->|Create Template| SSSSS[Create Tour Template]
    RRRRR -->|Edit Template| TTTTT[Edit Tour Template]
    RRRRR -->|Use Template| UUUUU[Use Existing Template]
    
    SSSSS --> VVVVV[Define Template Structure]
    TTTTT --> WWWWW[Modify Template]
    UUUUU --> XXXXX[Apply Template to New Tour]
    
    VVVVV --> YYYYY[Save New Template]
    WWWWW --> ZZZZZ[Update Template]
    XXXXX --> AAAAAA[Create Tour from Template]
    
    YYYYY --> F
    ZZZZZ --> F
    AAAAAA --> F
    
    %% Business Rules
    BBBBBB[Business Rules] --> CCCCCC[Tour dates validation]
    CCCCCC --> DDDDDD[Budget approval limits]
    DDDDDD --> EEEEEE[Advance payment rules]
    EEEEEE --> FFFFFF[Expense reimbursement rules]
    FFFFFF --> GGGGGG[Tour duration limits]
    GGGGGG --> HHHHHH[Approval hierarchy compliance]
    HHHHHH --> FF
    
    %% Django Implementation
    IIIIII[Django Implementation] --> JJJJJJ[TourIntimation Model]
    JJJJJJ --> KKKKKK[TourExpense Model]
    KKKKKK --> LLLLLL[TourApproval Model]
    LLLLLL --> MMMMMM[TourTemplate Model]
    MMMMMM --> NNNNNN[TourIntimationForm Validation]
    NNNNNN --> OOOOOO[Tour Management Views]
    OOOOOO --> PPPPPP[Tour Approval Views]
    PPPPPP --> QQQQQQ[Tour Expense Views]
    QQQQQQ --> RRRRRR[SAP Fiori UI Templates]
    RRRRRR --> SSSSSS[HTMX Dynamic Updates]
    SSSSSS --> F
    
    %% Integration Points
    TTTTTT[Integration Points] --> UUUUUU[Employee Management]
    UUUUUU --> VVVVVV[Approval Workflow]
    VVVVVV --> WWWWWW[Expense Management]
    WWWWWW --> XXXXXX[Accounts Integration]
    XXXXXX --> YYYYYY[Calendar System]
    YYYYYY --> ZZZZZZ[Notification System]
    ZZZZZZ --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style II fill:#e8f5e8
    style VV fill:#e8f5e8
    style CCC fill:#e8f5e8
    style DDD fill:#ffebee
    style EEE fill:#fff3e0
    style AAAA fill:#e8f5e8
    style VVVV fill:#e8f5e8
    style WWWW fill:#e8f5e8
    style XXXX fill:#ffebee
    style YYYY fill:#e8f5e8
    style GG fill:#fff3e0
    style HH fill:#fff3e0
    style QQ fill:#fff3e0
    style YYY fill:#fff3e0
    style LLLL fill:#fff3e0
    style MMMM fill:#fff3e0
    style NNNN fill:#fff3e0
    style OOOO fill:#fff3e0
    style PPPP fill:#fff3e0
    style YYYYY fill:#e8f5e8
    style ZZZZZ fill:#e8f5e8
    style AAAAAA fill:#e8f5e8
    style IIIIII fill:#f1f8e9
    style BBBBBB fill:#e3f2fd
    style TTTTTT fill:#e0f2f1
