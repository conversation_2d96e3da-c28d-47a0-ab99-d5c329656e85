graph TD
    A[Format Document Menu] --> B[Page Load & Initialization]
    A --> C[Document Upload Section]
    A --> D[Document List Management]
    A --> E[Document Operations]

    %% Page Load & Initialization
    B --> B1[Load MROffice Page]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Load Module Dropdown]
    B3 --> B4[Populate Available Modules]
    B4 --> B5[Load Document Grid]
    B5 --> B6[Apply Company Filter]
    B6 --> B7[Display Initial Document List]
    B7 --> B8[Setup Event Handlers]
    B8 --> B9[Enable Search Functionality]
    B9 --> B10[Page Ready for User Interaction]

    %% Document Upload Section
    C --> C1[Upload Form Interface]
    C1 --> C2[Module Selection Dropdown]
    C2 --> C3[Select Target Module]
    C3 --> C4{Module Selected?}
    C4 -->|No| C5[Module Selection Required]
    C4 -->|Yes| C6[Format Description Field]
    
    C5 --> C3
    C6 --> C7[Enter Document Format/Description]
    C7 --> C8[File Upload Control]
    C8 --> C9[Browse and Select File]
    C9 --> C10{File Selected?}
    C10 -->|No| C11[File Selection Required]
    C10 -->|Yes| C12[Upload Button Click]
    
    C11 --> C9
    C12 --> C13[Client-side Validation]
    C13 --> C14[Check File Size]
    C14 --> C15{Size Valid?}
    C15 -->|No| C16[File Size Error Message]
    C15 -->|Yes| C17[Check File Type]
    
    C16 --> C9
    C17 --> C18{Type Valid?}
    C18 -->|No| C19[File Type Error Message]
    C18 -->|Yes| C20[Process File Upload]
    
    C19 --> C9
    C20 --> C21[Read File Stream]
    C21 --> C22[Convert to Binary Data]
    C22 --> C23[Extract File Properties]
    C23 --> C24[Prepare Database Record]
    C24 --> C25[Set System Information]
    C25 --> C26[Set Company ID]
    C26 --> C27[Set Session Details]
    C27 --> C28[Set Financial Year]
    C28 --> C29[Set Module ID]
    C29 --> C30[Set Format Description]
    C30 --> C31[Set File Name]
    C31 --> C32[Set File Size]
    C32 --> C33[Set Content Type]
    C33 --> C34[Set Binary Data]
    C34 --> C35[Execute Database Insert]
    C35 --> C36{Insert Successful?}
    C36 -->|No| C37[Database Error Message]
    C36 -->|Yes| C38[Upload Success Message]
    
    C37 --> C6
    C38 --> C39[Clear Upload Form]
    C39 --> C40[Refresh Document Grid]
    C40 --> C41[Reset File Upload Control]

    %% Document List Management
    D --> D1[Document Grid Display]
    D1 --> D2[Load Documents from Database]
    D2 --> D3[Apply Company Filter]
    D3 --> D4[Join with Module Master]
    D4 --> D5[Get Module Names]
    D5 --> D6[Sort by Module]
    D6 --> D7[Apply Pagination]
    D7 --> D8[Bind to GridView]
    D8 --> D9[Display Document List]
    
    D --> D10[Search Functionality]
    D10 --> D11[Module Filter Dropdown]
    D11 --> D12[Select Module Filter]
    D12 --> D13{Module Filter Applied?}
    D13 -->|Yes| D14[Filter by Selected Module]
    D13 -->|No| D15[Show All Modules]
    
    D14 --> D16[Apply Module Filter]
    D15 --> D16
    D16 --> D17[Format Search TextBox]
    D17 --> D18[Enter Search Term]
    D18 --> D19{Search Term Entered?}
    D19 -->|Yes| D20[Filter by Format Description]
    D19 -->|No| D21[Show All Formats]
    
    D20 --> D22[Apply Format Filter]
    D21 --> D22
    D22 --> D23[Execute Filtered Query]
    D23 --> D24[Update Document Grid]
    D24 --> D25[Display Filtered Results]
    D25 --> D26[Update Result Count]

    %% Document Operations
    E --> E1[View Document Details]
    E --> E2[Download Document]
    E --> E3[Delete Document]
    
    %% View Document Details
    E1 --> E1a[Select Document Row]
    E1a --> E1b[Get Document ID]
    E1b --> E1c[Load Document Record]
    E1c --> E1d[Display Document Information]
    E1d --> E1e[Show Module Name]
    E1e --> E1f[Show Format Description]
    E1f --> E1g[Show File Name]
    E1g --> E1h[Show File Size]
    E1h --> E1i[Show Content Type]
    E1i --> E1j[Show Upload Date/Time]
    E1j --> E1k[Show Uploaded By User]
    E1k --> E1l[Show Company Information]
    
    %% Download Document
    E2 --> E2a[Click Download Link]
    E2a --> E2b[Validate User Access]
    E2b --> E2c{Access Authorized?}
    E2c -->|No| E2d[Access Denied Message]
    E2c -->|Yes| E2e[Get Document ID]
    
    E2d --> D9
    E2e --> E2f[Load Document Record]
    E2f --> E2g[Check Document Exists]
    E2g --> E2h{Document Found?}
    E2h -->|No| E2i[Document Not Found Error]
    E2h -->|Yes| E2j[Retrieve Binary Data]
    
    E2i --> D9
    E2j --> E2k[Set Response Headers]
    E2k --> E2l[Set Content-Type Header]
    E2l --> E2m[Set Content-Disposition Header]
    E2m --> E2n[Set File Name in Header]
    E2n --> E2o[Write Binary Data to Response]
    E2o --> E2p[Stream File to Browser]
    E2p --> E2q[Log Download Activity]
    E2q --> E2r[Update Download Counter]
    
    %% Delete Document
    E3 --> E3a[Click Delete Button]
    E3a --> E3b[Confirm Deletion Dialog]
    E3b --> E3c{Confirm Delete?}
    E3c -->|No| E3d[Cancel Deletion]
    E3c -->|Yes| E3e[Validate Delete Permission]
    
    E3d --> D9
    E3e --> E3f{Delete Permission?}
    E3f -->|No| E3g[Permission Denied Message]
    E3f -->|Yes| E3h[Get Document ID]
    
    E3g --> D9
    E3h --> E3i[Check Document Dependencies]
    E3i --> E3j{Has Dependencies?}
    E3j -->|Yes| E3k[Cannot Delete - Dependencies Exist]
    E3j -->|No| E3l[Execute Delete Query]
    
    E3k --> D9
    E3l --> E3m{Delete Successful?}
    E3m -->|No| E3n[Delete Error Message]
    E3m -->|Yes| E3o[Delete Success Message]
    
    E3n --> D9
    E3o --> E3p[Refresh Document Grid]
    E3p --> E3q[Update Document Count]
    E3q --> D9

    %% Error Handling & Validation
    A --> F[Error Handling]
    F --> F1[File Upload Errors]
    F1 --> F1a[File Size Exceeded]
    F1a --> F1b[Invalid File Type]
    F1b --> F1c[Upload Timeout]
    F1c --> F1d[Database Connection Error]
    F1d --> F1e[Insufficient Storage Space]
    
    F --> F2[Access Control Errors]
    F2 --> F2a[Unauthorized Access]
    F2a --> F2b[Session Expired]
    F2b --> F2c[Invalid Permissions]
    
    F --> F3[System Errors]
    F3 --> F3a[Database Errors]
    F3a --> F3b[Network Errors]
    F3b --> F3c[Server Errors]

    %% Real-time Features
    A --> G[Real-time Features]
    G --> G1[Auto-refresh Document List]
    G1 --> G2[Live Search Results]
    G2 --> G3[Upload Progress Indicator]
    G3 --> G4[Real-time File Validation]
    G4 --> G5[Dynamic Module Loading]
    
    %% AJAX Operations
    A --> H[AJAX Operations]
    H --> H1[Asynchronous File Upload]
    H1 --> H2[Dynamic Grid Refresh]
    H2 --> H3[Live Search Without Postback]
    H3 --> H4[Modal Dialog Operations]
    H4 --> H5[Progress Bar Updates]

    %% Security Features
    A --> I[Security Features]
    I --> I1[File Type Validation]
    I1 --> I2[File Size Restrictions]
    I2 --> I3[Virus Scanning]
    I3 --> I4[Access Control Validation]
    I4 --> I5[SQL Injection Prevention]
    I5 --> I6[XSS Protection]
    I6 --> I7[CSRF Token Validation]

    %% Performance Optimization
    A --> J[Performance Optimization]
    J --> J1[Efficient File Streaming]
    J1 --> J2[Database Query Optimization]
    J2 --> J3[Pagination for Large Datasets]
    J3 --> J4[Caching Strategies]
    J4 --> J5[Compressed File Storage]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef uploadClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef listClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef operationClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B10 initClass
    class C,C1,C20,C35,C38 uploadClass
    class D,D1,D9,D16,D25 listClass
    class E,E1,E2,E3 operationClass
    class C4,C10,C15,C18,C36,D13,D19,E2c,E2h,E3c,E3f,E3j,E3m decisionClass
    class C5,C11,C16,C19,C37,E2d,E2i,E3g,E3k,E3n errorClass
    class C38,C40,E3o,E3p successClass
