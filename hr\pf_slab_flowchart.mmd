flowchart TD
    A[User Access PF Slab Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load PF Slab Master Page]
    
    D --> E[Display PF Slab Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_PF_Slab]
    G --> H[Display Grid with Pagination]
    H --> I[Show Slab Range & Percentage]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New PF Slab
    F -->|Add New Slab| K[Show Add PF Slab Form]
    K --> L[User Enters Slab Details]
    L --> M[Minimum Salary Input]
    M --> N[Maximum Salary Input]
    N --> O[Employee PF Percentage Input]
    O --> P[Company PF Percentage Input]
    P --> Q[ESI Percentage Input]
    Q --> R{Validation Check}
    R -->|Empty Fields| S[Show Required Field Error]
    S --> L
    R -->|Invalid Range| T[Show Range Validation Error]
    T --> L
    R -->|Overlapping Slab| U[Show Overlap Error]
    U --> L
    R -->|Valid Data| V[Insert into tblHR_PF_Slab]
    V --> W{Insert Success?}
    W -->|Success| X[Show 'PF Slab Added' Message]
    W -->|Error| Y[Show Error Message]
    X --> Z[Refresh Grid Display]
    Y --> Z
    Z --> F
    
    %% Edit PF Slab
    F -->|Edit Record| AA[Enable Edit Mode for Row]
    AA --> BB[User Modifies Slab Details]
    BB --> CC{Update Validation}
    CC -->|Invalid| DD[Show Validation Error]
    DD --> BB
    CC -->|Valid| EE[Confirm Update Action]
    EE --> FF{User Confirms?}
    FF -->|No| GG[Cancel Edit Mode]
    GG --> F
    FF -->|Yes| HH[Update tblHR_PF_Slab Record]
    HH --> II{Update Success?}
    II -->|Success| JJ[Show 'Record Updated' Message]
    II -->|Error| KK[Show Update Error]
    JJ --> LL[Refresh Grid Display]
    KK --> LL
    LL --> F
    
    %% Delete PF Slab
    F -->|Delete Record| MM[Show Delete Confirmation]
    MM --> NN{User Confirms Delete?}
    NN -->|No| F
    NN -->|Yes| OO[Check PF Slab Usage]
    OO --> PP{Slab in Use?}
    PP -->|Yes| QQ[Show 'Cannot Delete - In Use' Error]
    QQ --> F
    PP -->|No| RR[Delete from tblHR_PF_Slab]
    RR --> SS{Delete Success?}
    SS -->|Success| TT[Show 'Record Deleted' Message]
    SS -->|Error| UU[Show Delete Error]
    TT --> VV[Refresh Grid Display]
    UU --> VV
    VV --> F
    
    %% PF Slab Configuration
    F -->|Configure Slabs| WW[Load PF Slab Configuration]
    WW --> XX{Configuration Type}
    XX -->|Salary Slabs| YY[Configure Salary-based Slabs]
    XX -->|Percentage Rules| ZZ[Configure PF Percentage Rules]
    XX -->|ESI Rules| AAA[Configure ESI Rules]
    XX -->|Exemption Rules| BBB[Configure Exemption Rules]
    
    YY --> CCC[Define Salary Ranges]
    ZZ --> DDD[Define PF Percentages]
    AAA --> EEE[Define ESI Percentages]
    BBB --> FFF[Define Exemption Criteria]
    
    CCC --> GGG[Validate Slab Ranges]
    DDD --> GGG
    EEE --> GGG
    FFF --> GGG
    GGG --> HHH[Save Slab Configuration]
    HHH --> F
    
    %% PF Calculation Engine
    F -->|Test Calculation| III[Load PF Calculation Test]
    III --> JJJ[Enter Test Salary]
    JJJ --> KKK[Calculate PF Components]
    KKK --> LLL[Employee PF Calculation]
    LLL --> MMM[Company PF Calculation]
    MMM --> NNN[ESI Calculation]
    NNN --> OOO[Total Deduction Calculation]
    OOO --> PPP[Display Calculation Results]
    PPP --> QQQ[Show Slab Applied]
    QQQ --> RRR[Show Percentage Used]
    RRR --> SSS[Show Amount Breakdown]
    SSS --> F
    
    %% PF Slab Reports
    F -->|Slab Reports| TTT[PF Slab Reporting Interface]
    TTT --> UUU{Report Type}
    UUU -->|Slab Summary| VVV[PF Slab Summary Report]
    UUU -->|Usage Analysis| WWW[Slab Usage Analysis]
    UUU -->|Employee Distribution| XXX[Employee Distribution by Slab]
    UUU -->|Calculation Report| YYY[PF Calculation Report]
    
    VVV --> ZZZ[Generate Slab Summary]
    WWW --> AAAA[Generate Usage Analysis]
    XXX --> BBBB[Generate Distribution Report]
    YYY --> CCCC[Generate Calculation Report]
    
    ZZZ --> DDDD[Export PF Slab Reports]
    AAAA --> DDDD
    BBBB --> DDDD
    CCCC --> DDDD
    DDDD --> F
    
    %% Statutory Compliance
    F -->|Compliance| EEEE[PF Statutory Compliance]
    EEEE --> FFFF{Compliance Type}
    FFFF -->|PF Rules| GGGG[PF Act Compliance]
    FFFF -->|ESI Rules| HHHH[ESI Act Compliance]
    FFFF -->|Rate Updates| IIII[Statutory Rate Updates]
    FFFF -->|Return Filing| JJJJ[PF Return Preparation]
    
    GGGG --> KKKK[Validate PF Rules]
    HHHH --> LLLL[Validate ESI Rules]
    IIII --> MMMM[Update Statutory Rates]
    JJJJ --> NNNN[Prepare PF Returns]
    
    KKKK --> OOOO[Generate Compliance Report]
    LLLL --> OOOO
    MMMM --> OOOO
    NNNN --> OOOO
    OOOO --> F
    
    %% Slab History Management
    F -->|Slab History| PPPP[PF Slab History Management]
    PPPP --> QQQQ[Display Historical Slabs]
    QQQQ --> RRRR[Show Effective Dates]
    RRRR --> SSSS[Show Rate Changes]
    SSSS --> TTTT{History Action}
    TTTT -->|View Details| UUUU[View Historical Details]
    TTTT -->|Compare Slabs| VVVV[Compare Slab Changes]
    TTTT -->|Restore Slab| WWWW[Restore Historical Slab]
    
    UUUU --> XXXX[Display Detailed History]
    VVVV --> YYYY[Show Comparison Report]
    WWWW --> ZZZZ[Restore Previous Configuration]
    
    XXXX --> F
    YYYY --> F
    ZZZZ --> F
    
    %% Business Rules
    AAAAA[Business Rules] --> BBBBB[Salary ranges must not overlap]
    BBBBB --> CCCCC[Minimum salary < Maximum salary]
    CCCCC --> DDDDD[PF percentage within statutory limits]
    DDDDD --> EEEEE[ESI percentage within statutory limits]
    EEEEE --> FFFFF[Slab effective date validation]
    FFFFF --> GGGGG[Mandatory slab coverage]
    GGGGG --> R
    
    %% Django Implementation
    HHHHH[Django Implementation] --> IIIII[PFSlab Model]
    IIIII --> JJJJJ[PFSlabHistory Model]
    JJJJJ --> KKKKK[PFCalculation Model]
    KKKKK --> LLLLL[PFSlabForm Validation]
    LLLLL --> MMMMM[PF Slab Management Views]
    MMMMM --> NNNNN[PF Calculation Views]
    NNNNN --> OOOOO[Compliance Management Views]
    OOOOO --> PPPPP[SAP Fiori UI Templates]
    PPPPP --> QQQQQ[HTMX Dynamic Updates]
    QQQQQ --> F
    
    %% Integration Points
    RRRRR[Integration Points] --> SSSSS[Payroll Processing]
    SSSSS --> TTTTT[Employee Management]
    TTTTT --> UUUUU[Salary Calculation]
    UUUUU --> VVVVV[Statutory Compliance]
    VVVVV --> WWWWW[Tax Calculation]
    WWWWW --> XXXXX[Financial Reporting]
    XXXXX --> D
    
    %% Calculation Engine
    YYYYY[Calculation Engine] --> ZZZZZ[Slab Identification Logic]
    ZZZZZ --> AAAAAA[PF Amount Calculation]
    AAAAAA --> BBBBBB[ESI Amount Calculation]
    BBBBBB --> CCCCCC[Exemption Application]
    CCCCCC --> DDDDDD[Total Deduction Computation]
    DDDDDD --> III
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style V fill:#e8f5e8
    style HH fill:#e8f5e8
    style RR fill:#ffebee
    style HHH fill:#e8f5e8
    style PPP fill:#e8f5e8
    style S fill:#fff3e0
    style T fill:#fff3e0
    style U fill:#fff3e0
    style QQ fill:#fff3e0
    style ZZZ fill:#fff3e0
    style AAAA fill:#fff3e0
    style BBBB fill:#fff3e0
    style CCCC fill:#fff3e0
    style OOOO fill:#e8f5e8
    style HHHHH fill:#f1f8e9
    style AAAAA fill:#e3f2fd
    style RRRRR fill:#e0f2f1
    style YYYYY fill:#f3e5f5
