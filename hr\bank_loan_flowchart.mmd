flowchart TD
    A[User Access Bank Loan Management] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Bank Loan Management Page]
    
    D --> E[Display Bank Loan Grid]
    E --> F{User Action}
    
    %% View Bank Loans
    F -->|View Loans| G[Query tblHR_BankLoan]
    G --> H[Display Loan Grid with Pagination]
    H --> I[Show Employee, Loan Type, Amount, Status]
    I --> J[Show Edit/Delete/Process Links]
    J --> F
    
    %% Create New Bank Loan
    F -->|Create Loan| K[Load Bank Loan Application Form]
    K --> L[Employee Information Section]
    L --> M[Employee Selection]
    M --> N[Employee Details Auto-populate]
    N --> O[Salary Information Display]
    O --> P[Loan Details Section]
    P --> Q[Loan Type Selection]
    Q --> R[Loan Amount Input]
    R --> S[Loan Purpose Input]
    S --> T[Repayment Period Selection]
    T --> U[Interest Rate Input]
    U --> V[EMI Calculation]
    V --> W[Guarantor Information]
    W --> X[Guarantor 1 Details]
    X --> Y[Guarantor 2 Details]
    Y --> Z[Document Upload Section]
    Z --> AA[Salary Certificate Upload]
    AA --> BB[Bank Statement Upload]
    BB --> CC[Property Documents Upload]
    CC --> DD[Other Supporting Documents]
    DD --> EE{Validation Check}
    EE -->|Invalid Amount| FF[Show Amount Validation Error]
    FF --> K
    EE -->|Invalid EMI| GG[Show EMI Validation Error]
    GG --> K
    EE -->|Missing Documents| HH[Show Document Required Error]
    HH --> K
    EE -->|Valid Data| II[Calculate Loan Eligibility]
    II --> JJ{Eligibility Check}
    JJ -->|Not Eligible| KK[Show Eligibility Error]
    KK --> K
    JJ -->|Eligible| LL[Insert into tblHR_BankLoan]
    LL --> MM[Set Status to Applied]
    MM --> NN[Generate Loan Application ID]
    NN --> OO[Send for Processing]
    OO --> PP[Loan Application Created]
    PP --> F
    
    %% Edit Bank Loan
    F -->|Edit Loan| QQ[Load Loan for Editing]
    QQ --> RR[Check Loan Status]
    RR --> SS{Status Check}
    SS -->|Approved/Disbursed| TT[Show 'Cannot Edit - Processed' Error]
    TT --> F
    SS -->|Applied/Under Review| UU[Enable Loan Editing]
    UU --> VV[User Modifies Loan Details]
    VV --> WW{Update Validation}
    WW -->|Invalid| XX[Show Validation Error]
    XX --> VV
    WW -->|Valid| YY[Update tblHR_BankLoan]
    YY --> ZZ[Reset Processing Status if needed]
    ZZ --> AAA[Loan Update Complete]
    AAA --> F
    
    %% Loan Processing Workflow
    F -->|Process Loans| BBB[Load Loan Processing Interface]
    BBB --> CCC[Display Pending Loan Applications]
    CCC --> DDD[Show Loan Details for Review]
    DDD --> EEE{Processing Decision}
    EEE -->|Approve| FFF[Approve Loan]
    EEE -->|Reject| GGG[Reject Loan]
    EEE -->|Request Documents| HHH[Request Additional Documents]
    EEE -->|Forward to Bank| III[Forward to Bank]
    
    FFF --> JJJ[Update Status to Approved]
    GGG --> KKK[Update Status to Rejected]
    HHH --> LLL[Update Status to Documents Required]
    III --> MMM[Update Status to Forwarded to Bank]
    
    JJJ --> NNN[Set Approved By & Date]
    KKK --> OOO[Set Rejection Reason]
    LLL --> PPP[Set Document Request Details]
    MMM --> QQQ[Set Bank Forward Details]
    
    NNN --> RRR[Generate Approval Letter]
    OOO --> SSS[Send Rejection Notification]
    PPP --> TTT[Send Document Request]
    QQQ --> UUU[Send Bank Forward Notice]
    
    RRR --> VVV[Send Approval Notification]
    SSS --> F
    TTT --> F
    UUU --> F
    VVV --> F
    
    %% Loan Disbursement Management
    F -->|Manage Disbursement| WWW[Loan Disbursement Management]
    WWW --> XXX[Display Approved Loans]
    XXX --> YYY[Select Loan for Disbursement]
    YYY --> ZZZ[Verify Bank Approval]
    ZZZ --> AAAA[Enter Disbursement Details]
    AAAA --> BBBB[Disbursement Amount]
    BBBB --> CCCC[Disbursement Date]
    CCCC --> DDDD[Bank Reference Number]
    DDDD --> EEEE[Account Details Verification]
    EEEE --> FFFF[Process Disbursement]
    FFFF --> GGGG[Update Status to Disbursed]
    GGGG --> HHHH[Generate Disbursement Record]
    HHHH --> IIII[Send Disbursement Notification]
    IIII --> F
    
    %% EMI Management
    F -->|Manage EMI| JJJJ[EMI Management Interface]
    JJJJ --> KKKK[Display Active Loans]
    KKKK --> LLLL[Show EMI Schedule]
    LLLL --> MMMM{EMI Action}
    MMMM -->|Record Payment| NNNN[Record EMI Payment]
    MMMM -->|Generate Schedule| OOOO[Generate EMI Schedule]
    MMMM -->|Update EMI| PPPP[Update EMI Amount]
    MMMM -->|Mark Default| QQQQ[Mark EMI Default]
    
    NNNN --> RRRR[Enter Payment Details]
    OOOO --> SSSS[Calculate EMI Schedule]
    PPPP --> TTTT[Modify EMI Amount]
    QQQQ --> UUUU[Record Default Details]
    
    RRRR --> VVVV[Update Payment Status]
    SSSS --> WWWW[Generate Schedule Report]
    TTTT --> XXXX[Update Loan Terms]
    UUUU --> YYYY[Update Default Status]
    
    VVVV --> ZZZZ[EMI Payment Recorded]
    WWWW --> AAAAA[EMI Schedule Generated]
    XXXX --> BBBBB[EMI Updated]
    YYYY --> CCCCC[Default Recorded]
    
    ZZZZ --> F
    AAAAA --> F
    BBBBB --> F
    CCCCC --> F
    
    %% Loan Reports
    F -->|Loan Reports| DDDDD[Bank Loan Reporting]
    DDDDD --> EEEEE{Report Type}
    EEEEE -->|Loan Summary| FFFFF[Loan Summary Report]
    EEEEE -->|Employee Loans| GGGGG[Employee-wise Loan Report]
    EEEEE -->|EMI Report| HHHHH[EMI Payment Report]
    EEEEE -->|Default Report| IIIII[Loan Default Report]
    EEEEE -->|Outstanding Report| JJJJJ[Outstanding Loan Report]
    
    FFFFF --> KKKKK[Generate Summary Report]
    GGGGG --> LLLLL[Generate Employee Report]
    HHHHH --> MMMMM[Generate EMI Report]
    IIIII --> NNNNN[Generate Default Report]
    JJJJJ --> OOOOO[Generate Outstanding Report]
    
    KKKKK --> PPPPP[Export Loan Reports]
    LLLLL --> PPPPP
    MMMMM --> PPPPP
    NNNNN --> PPPPP
    OOOOO --> PPPPP
    PPPPP --> F
    
    %% Loan Closure
    F -->|Close Loan| QQQQQ[Loan Closure Management]
    QQQQQ --> RRRRR[Select Loan for Closure]
    RRRRR --> SSSSS[Verify Outstanding Amount]
    SSSSS --> TTTTT{Outstanding Amount?}
    TTTTT -->|Yes| UUUUU[Process Final Payment]
    TTTTT -->|No| VVVVV[Process Loan Closure]
    
    UUUUU --> WWWWW[Enter Final Payment Details]
    VVVVV --> XXXXX[Generate Closure Certificate]
    
    WWWWW --> YYYYY[Update Final Payment]
    XXXXX --> ZZZZZ[Update Status to Closed]
    
    YYYYY --> AAAAAA[Mark Loan as Closed]
    ZZZZZ --> BBBBBB[Send Closure Notification]
    
    AAAAAA --> CCCCCC[Generate Closure Report]
    BBBBBB --> CCCCCC
    CCCCCC --> F
    
    %% Business Rules
    DDDDDD[Business Rules] --> EEEEEE[Loan amount within salary limits]
    EEEEEE --> FFFFFF[EMI not exceeding 40% of salary]
    FFFFFF --> GGGGGG[Minimum service period required]
    GGGGGG --> HHHHHH[Maximum loan tenure limits]
    HHHHHH --> IIIIII[Guarantor eligibility validation]
    IIIIII --> JJJJJJ[Document verification mandatory]
    JJJJJJ --> EE
    
    %% Django Implementation
    KKKKKK[Django Implementation] --> LLLLLL[BankLoan Model]
    LLLLLL --> MMMMMM[LoanEMI Model]
    MMMMMM --> NNNNNN[LoanGuarantor Model]
    NNNNNN --> OOOOOO[LoanDocument Model]
    OOOOOO --> PPPPPP[BankLoanForm Validation]
    PPPPPP --> QQQQQQ[Loan Management Views]
    QQQQQQ --> RRRRRR[Loan Processing Views]
    RRRRRR --> SSSSSS[EMI Management Views]
    SSSSSS --> TTTTTT[SAP Fiori UI Templates]
    TTTTTT --> UUUUUU[HTMX Dynamic Updates]
    UUUUUU --> F
    
    %% Integration Points
    VVVVVV[Integration Points] --> WWWWWW[Employee Management]
    WWWWWW --> XXXXXX[Payroll System]
    XXXXXX --> YYYYYY[Salary Deduction]
    YYYYYY --> ZZZZZZ[Banking System]
    ZZZZZZ --> AAAAAAA[Document Management]
    AAAAAAA --> BBBBBBB[Approval Workflow]
    BBBBBBB --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style LL fill:#e8f5e8
    style YY fill:#e8f5e8
    style FFF fill:#e8f5e8
    style GGG fill:#ffebee
    style HHH fill:#fff3e0
    style III fill:#e8f5e8
    style FFFF fill:#e8f5e8
    style VVVV fill:#e8f5e8
    style XXXX fill:#e8f5e8
    style YYYY fill:#ffebee
    style UUUUU fill:#e8f5e8
    style VVVVV fill:#e8f5e8
    style FF fill:#fff3e0
    style GG fill:#fff3e0
    style HH fill:#fff3e0
    style KK fill:#fff3e0
    style TT fill:#fff3e0
    style KKKKK fill:#fff3e0
    style LLLLL fill:#fff3e0
    style MMMMM fill:#fff3e0
    style NNNNN fill:#fff3e0
    style OOOOO fill:#fff3e0
    style KKKKKK fill:#f1f8e9
    style DDDDDD fill:#e3f2fd
    style VVVVVV fill:#e0f2f1
