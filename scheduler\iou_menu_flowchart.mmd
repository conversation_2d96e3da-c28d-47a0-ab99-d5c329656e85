graph TD
    A[IOU Menu] --> B[Page Load & Initialization]
    A --> C[IOU Creation]
    A --> D[IOU Management]
    A --> E[IOU Settlement]

    %% Page Load & Initialization
    B --> B1[Load IOU Dashboard]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Setup Navigation Menu]
    B3 --> B4[Load IOU Grid]
    B4 --> B5[Apply Company Filter]
    B5 --> B6[Apply Financial Year Filter]
    B6 --> B7[Display Outstanding IOUs]
    B7 --> B8[Setup Action Buttons]
    B8 --> B9[Enable Search Functionality]
    B9 --> B10[Calculate IOU Summary]
    B10 --> B11[Page Ready for User Interaction]

    %% IOU Creation
    C --> C1[IOU Type Selection]
    C --> C2[IOU Form Entry]
    C --> C3[IOU Validation]
    C --> C4[IOU Approval]

    %% IOU Type Selection
    C1 --> C1a[IOU Type Interface]
    C1a --> C1b{IOU Type}
    C1b -->|Employee Advance| C1c[Employee Advance IOU]
    C1b -->|Vendor Advance| C1d[Vendor Advance IOU]
    C1b -->|Expense IOU| C1e[Expense IOU]
    C1b -->|Petty Cash| C1f[Petty Cash IOU]
    C1b -->|Travel Advance| C1g[Travel Advance IOU]
    
    %% Employee Advance IOU
    C1c --> C1h[Employee Selection]
    C1h --> C1i[Browse Employee Master]
    C1i --> C1j[Select Employee]
    C1j --> C1k{Employee Selected?}
    C1k -->|No| C1l[Employee Selection Required]
    C1k -->|Yes| C1m[Load Employee Details]
    
    C1l --> C1i
    C1m --> C1n[Display Employee Information]
    C1n --> C1o[Check Employee Eligibility]
    C1o --> C1p{Employee Eligible?}
    C1p -->|No| C1q[Employee Not Eligible]
    C1p -->|Yes| C1r[Advance Amount Entry]
    
    C1q --> C1i
    C1r --> C1s[Purpose Entry]
    C1s --> C1t[Expected Settlement Date]
    C1t --> C1u[Approval Authority Selection]
    C1u --> C2
    
    %% Vendor Advance IOU
    C1d --> C1v[Vendor Selection]
    C1v --> C1w[Browse Vendor Master]
    C1w --> C1x[Select Vendor]
    C1x --> C1y{Vendor Selected?}
    C1y -->|No| C1z[Vendor Selection Required]
    C1y -->|Yes| C1aa[Load Vendor Details]
    
    C1z --> C1w
    C1aa --> C1bb[Display Vendor Information]
    C1bb --> C1cc[Check Vendor Status]
    C1cc --> C1dd{Vendor Active?}
    C1dd -->|No| C1ee[Vendor Not Active]
    C1dd -->|Yes| C1ff[Advance Amount Entry]
    
    C1ee --> C1w
    C1ff --> C1gg[Purchase Order Reference]
    C1gg --> C1hh[Work Order Reference]
    C1hh --> C1ii[Purpose Entry]
    C1ii --> C1jj[Expected Settlement Date]
    C1jj --> C2
    
    %% Expense IOU
    C1e --> C1kk[Expense Category Selection]
    C1kk --> C1ll[Expense Description]
    C1ll --> C1mm[Amount Entry]
    C1mm --> C1nn[Receipt Required]
    C1nn --> C1oo[Settlement Timeline]
    C1oo --> C2
    
    %% Travel Advance IOU
    C1g --> C1pp[Travel Details Entry]
    C1pp --> C1qq[Destination]
    C1qq --> C1rr[Travel Dates]
    C1rr --> C1ss[Travel Purpose]
    C1ss --> C1tt[Estimated Expenses]
    C1tt --> C1uu[Advance Amount]
    C1uu --> C2

    %% IOU Form Entry
    C2 --> C2a[Common Information Entry]
    C2a --> C2b[IOU Number Generation]
    C2b --> C2c[IOU Date Entry]
    C2c --> C2d[Amount Entry]
    C2d --> C2e[Currency Selection]
    C2e --> C2f[Purpose Description]
    C2f --> C2g[Settlement Terms]
    C2g --> C2h[Interest Terms]
    C2h --> C2i[Guarantor Information]
    C2i --> C2j[Supporting Documents]
    C2j --> C2k[Special Instructions]
    C2k --> C3

    %% IOU Validation
    C3 --> C3a[Form Validation]
    C3a --> C3b[Required Field Check]
    C3b --> C3c[Amount Validation]
    C3c --> C3d[Credit Limit Check]
    C3d --> C3e[Outstanding IOU Check]
    C3e --> C3f[Business Rule Validation]
    C3f --> C3g{Validation Passed?}
    C3g -->|No| C3h[Show Validation Errors]
    C3g -->|Yes| C3i[Save IOU]
    
    C3h --> C2
    C3i --> C3j[Generate IOU Number]
    C3j --> C3k[Set System Information]
    C3k --> C3l[Set Company ID]
    C3l --> C3m[Set Session Details]
    C3m --> C3n[Set Financial Year]
    C3n --> C3o[Set Status - Pending]
    C3o --> C3p[Execute Database Insert]
    C3p --> C3q{Insert Successful?}
    C3q -->|No| C3r[Database Error Message]
    C3q -->|Yes| C3s[IOU Creation Success]
    
    C3r --> C2
    C3s --> C4

    %% IOU Approval
    C4 --> C4a[Approval Workflow]
    C4a --> C4b{Approval Required?}
    C4b -->|No| C4c[Auto-Approve IOU]
    C4b -->|Yes| C4d[Route for Approval]
    
    C4c --> C4e[Set Status - Approved]
    C4d --> C4f[Send to Approver]
    C4f --> C4g[Approval Notification]
    C4g --> C4h[Pending Approval Status]
    
    C4e --> C4i[Generate IOU Document]
    C4h --> C4j[Wait for Approval]
    C4i --> C4k[Update Accounts]
    C4k --> C4l[Send Notifications]
    C4l --> C4m[Return to Dashboard]

    %% IOU Management
    D --> D1[IOU Search & Filter]
    D --> D2[IOU Operations]
    D --> D3[IOU Status Management]
    D --> D4[IOU Reports]

    %% IOU Search & Filter
    D1 --> D1a[Search Interface]
    D1a --> D1b[IOU Number Search]
    D1b --> D1c[Date Range Filter]
    D1c --> D1d[Type Filter]
    D1d --> D1e[Status Filter]
    D1e --> D1f[Employee/Vendor Filter]
    D1f --> D1g[Amount Range Filter]
    D1g --> D1h[Overdue Filter]
    D1h --> D1i[Apply Search Filters]
    D1i --> D1j[Execute Search Query]
    D1j --> D1k[Display Filtered Results]
    D1k --> D1l[Update Result Count]
    D1l --> D1m[Calculate Total Amounts]
    D1m --> D1n[Enable Export Options]

    %% IOU Operations
    D2 --> D2a[View IOU Details]
    D2a --> D2b[Edit IOU]
    D2b --> D2c[Cancel IOU]
    D2c --> D2d[Extend IOU]
    D2d --> D2e[Print IOU]
    D2e --> D2f[Partial Settlement]
    D2f --> D2g[Full Settlement]
    
    %% View IOU Details
    D2a --> D2h[Load IOU Record]
    D2h --> D2i[Display Complete Information]
    D2i --> D2j[Show Settlement History]
    D2j --> D2k[Show Approval History]
    D2k --> D2l[Show Related Documents]
    D2l --> D2m[Show Interest Calculations]
    D2m --> D2n[Show Outstanding Amount]
    
    %% Edit IOU
    D2b --> D2o[Check Edit Permission]
    D2o --> D2p{Edit Allowed?}
    D2p -->|No| D2q[Edit Not Allowed Message]
    D2p -->|Yes| D2r[Load Edit Form]
    
    D2q --> D1k
    D2r --> D2s[Pre-populate Fields]
    D2s --> D2t[Allow Modifications]
    D2t --> D2u[Validate Changes]
    D2u --> D2v{Changes Valid?}
    D2v -->|No| D2w[Show Validation Errors]
    D2v -->|Yes| D2x[Update IOU]
    
    D2w --> D2t
    D2x --> D2y[Set Modified Information]
    D2y --> D2z[Execute Database Update]
    D2z --> D2aa{Update Successful?}
    D2aa -->|No| D2bb[Update Error Message]
    D2aa -->|Yes| D2cc[Update Success Message]
    
    D2bb --> D2t
    D2cc --> D2dd[Send Update Notifications]
    D2dd --> D1k

    %% IOU Status Management
    D3 --> D3a[Status Tracking]
    D3a --> D3b[Pending IOUs]
    D3b --> D3c[Approved IOUs]
    D3c --> D3d[Active IOUs]
    D3d --> D3e[Overdue IOUs]
    D3e --> D3f[Partially Settled IOUs]
    D3f --> D3g[Fully Settled IOUs]
    D3g --> D3h[Cancelled IOUs]
    D3h --> D3i[Status Change Options]
    D3i --> D3j[Bulk Status Updates]

    %% IOU Reports
    D4 --> D4a[Outstanding IOU Report]
    D4a --> D4b[Overdue IOU Report]
    D4b --> D4c[Settlement Report]
    D4c --> D4d[Employee IOU Report]
    D4d --> D4e[Vendor IOU Report]
    D4e --> D4f[Interest Calculation Report]
    D4f --> D4g[Aging Analysis Report]
    D4g --> D4h[Export Report Options]

    %% IOU Settlement
    E --> E1[Settlement Processing]
    E --> E2[Settlement Verification]
    E --> E3[Account Reconciliation]
    E --> E4[Settlement Reports]

    %% Settlement Processing
    E1 --> E1a[Select IOU for Settlement]
    E1a --> E1b[Load IOU Details]
    E1b --> E1c[Calculate Outstanding Amount]
    E1c --> E1d[Calculate Interest]
    E1d --> E1e[Settlement Type Selection]
    E1e --> E1f{Settlement Type}
    E1f -->|Full Settlement| E1g[Full Settlement Process]
    E1f -->|Partial Settlement| E1h[Partial Settlement Process]
    
    %% Full Settlement Process
    E1g --> E1i[Enter Settlement Amount]
    E1i --> E1j[Verify Full Amount]
    E1j --> E1k{Amount Correct?}
    E1k -->|No| E1l[Amount Mismatch Error]
    E1k -->|Yes| E1m[Payment Method Selection]
    
    E1l --> E1i
    E1m --> E1n[Payment Details Entry]
    E1n --> E1o[Settlement Date Entry]
    E1o --> E1p[Settlement Reference]
    E1p --> E1q[Settlement Remarks]
    E1q --> E2
    
    %% Partial Settlement Process
    E1h --> E1r[Enter Partial Amount]
    E1r --> E1s[Validate Partial Amount]
    E1s --> E1t{Amount Valid?}
    E1t -->|No| E1u[Invalid Amount Error]
    E1t -->|Yes| E1v[Payment Method Selection]
    
    E1u --> E1r
    E1v --> E1w[Payment Details Entry]
    E1w --> E1x[Settlement Date Entry]
    E1x --> E1y[Settlement Reference]
    E1y --> E1z[Settlement Remarks]
    E1z --> E1aa[Calculate Remaining Balance]
    E1aa --> E2

    %% Settlement Verification
    E2 --> E2a[Settlement Validation]
    E2a --> E2b[Verify Payment Details]
    E2b --> E2c[Check Authorization]
    E2c --> E2d[Validate Settlement Amount]
    E2d --> E2e{Settlement Valid?}
    E2e -->|No| E2f[Settlement Validation Errors]
    E2e -->|Yes| E2g[Process Settlement]
    
    E2f --> E1
    E2g --> E2h[Update IOU Status]
    E2h --> E2i[Create Settlement Record]
    E2i --> E2j[Update Outstanding Amount]
    E2j --> E2k[Generate Settlement Number]
    E2k --> E2l[Execute Database Update]
    E2l --> E2m{Update Successful?}
    E2m -->|No| E2n[Settlement Error Message]
    E2m -->|Yes| E2o[Settlement Success Message]
    
    E2n --> E1
    E2o --> E3

    %% Account Reconciliation
    E3 --> E3a[Account Update Process]
    E3a --> E3b[Update Employee/Vendor Account]
    E3b --> E3c[Update Cash/Bank Account]
    E3c --> E3d[Create Journal Entries]
    E3d --> E3e[Post to General Ledger]
    E3e --> E3f[Update Trial Balance]
    E3f --> E3g[Reconciliation Verification]
    E3g --> E3h{Reconciliation Successful?}
    E3h -->|No| E3i[Reconciliation Error]
    E3h -->|Yes| E3j[Reconciliation Complete]
    
    E3i --> E3a
    E3j --> E4

    %% Settlement Reports
    E4 --> E4a[Settlement Summary Report]
    E4a --> E4b[Payment Receipt Generation]
    E4b --> E4c[Settlement Certificate]
    E4c --> E4d[Account Statement Update]
    E4d --> E4e[Notification Generation]
    E4e --> E4f[Settlement Documentation]
    E4f --> E4g[Archive Settlement Records]

    %% Integration Features
    A --> F[Integration Features]
    F --> F1[HR Integration]
    F1 --> F2[Accounts Integration]
    F2 --> F3[Payroll Integration]
    F3 --> F4[Expense Management Integration]
    F4 --> F5[Banking Integration]

    %% Workflow Management
    A --> G[Workflow Management]
    G --> G1[Approval Workflows]
    G1 --> G2[Notification System]
    G2 --> G3[Alert Management]
    G3 --> G4[Escalation Management]
    G4 --> G5[Reminder System]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef creationClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef managementClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef settlementClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B11 initClass
    class C,C1,C2,C3,C4 creationClass
    class D,D1,D2,D3,D4 managementClass
    class E,E1,E2,E3,E4 settlementClass
    class C1b,C1k,C1p,C1y,C1dd,C3g,C3q,C4b,D2p,D2v,D2aa,E1f,E1k,E1t,E2e,E2m,E3h decisionClass
    class C1l,C1q,C1z,C1ee,C3h,C3r,D2q,D2w,D2bb,E1l,E1u,E2f,E2n,E3i errorClass
    class C3s,D2cc,E2o,E3j successClass
