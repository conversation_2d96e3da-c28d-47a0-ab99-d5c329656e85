flowchart TD
    A[User Access Designation Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Designation Master Page]
    
    D --> E[Display Designation Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_Designation]
    G --> H[Display Grid with Pagination]
    H --> I[Show Type & Symbol]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New Designation
    F -->|Add New| K[Show Add Form in Footer]
    K --> L[User Enters Type & Symbol]
    L --> M{Validation Check}
    M -->|Empty Fields| N[Show Required Field Error]
    N --> L
    M -->|Valid Data| O[Check Type Uniqueness]
    O --> P{Type Exists?}
    P -->|Yes| Q[Show 'Designation Already Exists' Alert]
    Q --> L
    P -->|No| R[Check Symbol Uniqueness]
    R --> S{Symbol Exists?}
    S -->|Yes| T[Show 'Symbol Already Exists' Alert]
    T --> L
    S -->|No| U[Insert into tblHR_Designation]
    U --> V{Insert Success?}
    V -->|Success| W[Show 'Record Inserted' Message]
    V -->|Error| X[Show Error Message]
    W --> Y[Refresh Grid Display]
    X --> Y
    Y --> F
    
    %% Edit Designation
    F -->|Edit Record| Z[Enable Edit Mode for Row]
    Z --> AA[User Modifies Type/Symbol]
    AA --> BB{Update Validation}
    BB -->|Invalid| CC[Show Validation Error]
    CC --> AA
    BB -->|Valid| DD[Confirm Update Action]
    DD --> EE{User Confirms?}
    EE -->|No| FF[Cancel Edit Mode]
    FF --> F
    EE -->|Yes| GG[Update tblHR_Designation Record]
    GG --> HH{Update Success?}
    HH -->|Success| II[Show 'Record Updated' Message]
    HH -->|Error| JJ[Show Update Error]
    II --> KK[Refresh Grid Display]
    JJ --> KK
    KK --> F
    
    %% Delete Designation
    F -->|Delete Record| LL[Show Delete Confirmation]
    LL --> MM{User Confirms Delete?}
    MM -->|No| F
    MM -->|Yes| NN[Check Designation Usage]
    NN --> OO{Designation in Use?}
    OO -->|Yes| PP[Show 'Cannot Delete - In Use' Error]
    PP --> F
    OO -->|No| QQ[Delete from tblHR_Designation]
    QQ --> RR{Delete Success?}
    RR -->|Success| SS[Show 'Record Deleted' Message]
    RR -->|Error| TT[Show Delete Error]
    SS --> UU[Refresh Grid Display]
    TT --> UU
    UU --> F
    
    %% Designation Hierarchy Management
    F -->|Manage Hierarchy| VV[Load Designation Hierarchy]
    VV --> WW[Display Hierarchical Structure]
    WW --> XX[Show Reporting Relationships]
    XX --> YY[Enable Hierarchy Editing]
    YY --> ZZ[Update Reporting Structure]
    ZZ --> AAA[Save Hierarchy Changes]
    AAA --> F
    
    %% Business Rules
    BBB[Business Rules] --> CCC[Type must be unique]
    CCC --> DDD[Symbol must be unique]
    DDD --> EEE[Type is required]
    EEE --> FFF[Symbol is required]
    FFF --> GGG[Type minimum 3 characters]
    GGG --> HHH[Symbol maximum 5 characters]
    HHH --> III[Hierarchy validation]
    III --> M
    
    %% Usage Validation
    JJJ[Usage Validation] --> KKK[Check Employee Records]
    KKK --> LLL[Check Offer Letters]
    LLL --> MMM[Check Increment Records]
    MMM --> NNN[Check Promotion Records]
    NNN --> OOO[Check Salary Records]
    OOO --> NN
    
    %% Django Implementation
    PPP[Django Implementation] --> QQQ[Designation Model]
    QQQ --> RRR[DesignationForm Validation]
    RRR --> SSS[Designation CRUD Views]
    SSS --> TTT[Hierarchy Management Views]
    TTT --> UUU[SAP Fiori UI Templates]
    UUU --> VVV[HTMX Dynamic Updates]
    VVV --> F
    
    %% Integration Points
    WWW[Integration Points] --> XXX[Employee Management]
    XXX --> YYY[Offer Letter System]
    YYY --> ZZZ[Payroll Processing]
    ZZZ --> AAAA[Performance Management]
    AAAA --> BBBB[Promotion Workflow]
    BBBB --> CCCC[Organizational Chart]
    CCCC --> D
    
    %% Hierarchy Features
    DDDD[Hierarchy Features] --> EEEE[Reporting Structure]
    EEEE --> FFFF[Level Management]
    FFFF --> GGGG[Authority Matrix]
    GGGG --> HHHH[Approval Hierarchy]
    HHHH --> IIII[Delegation Rules]
    IIII --> VV
    
    %% Career Path Management
    JJJJ[Career Path] --> KKKK[Promotion Path Definition]
    KKKK --> LLLL[Skill Requirements]
    LLLL --> MMMM[Experience Criteria]
    MMMM --> NNNN[Performance Standards]
    NNNN --> OOOO[Career Progression Rules]
    OOOO --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style U fill:#e8f5e8
    style GG fill:#e8f5e8
    style QQ fill:#ffebee
    style Q fill:#fff3e0
    style T fill:#fff3e0
    style N fill:#fff3e0
    style VV fill:#e8f5e8
    style PPP fill:#f1f8e9
    style BBB fill:#e3f2fd
    style JJJ fill:#e8f5e8
    style WWW fill:#e0f2f1
    style DDDD fill:#f3e5f5
    style JJJJ fill:#fff8e1
