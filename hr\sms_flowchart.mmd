flowchart TD
    A[User Access SMS Management] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load SMS Management Dashboard]
    
    D --> E[Display SMS Management Interface]
    E --> F{SMS Action}
    
    %% Send Individual SMS
    F -->|Send Individual SMS| G[Load Individual SMS Form]
    G --> H[Mobile Number Input]
    H --> I[Message Content Input]
    I --> J[Message Type Selection]
    J --> K[Priority Level Selection]
    K --> L[Delivery Time Selection]
    L --> M{Validation Check}
    M -->|Invalid Mobile| N[Show Mobile Number Error]
    N --> G
    M -->|Empty Message| O[Show Message Required Error]
    O --> G
    M -->|Valid Data| P[Send Individual SMS]
    P --> Q[Log SMS Activity]
    Q --> R[Update SMS Status]
    R --> S[Show Send Confirmation]
    S --> F
    
    %% Send Bulk SMS
    F -->|Send Bulk SMS| T[Load Bulk SMS Interface]
    T --> U{Recipient Selection}
    U -->|All Employees| V[Select All Employees]
    U -->|Department-wise| W[Select by Department]
    U -->|Designation-wise| X[Select by Designation]
    U -->|Custom List| Y[Upload Custom Mobile List]
    U -->|Business Group| Z[Select by Business Group]
    
    V --> AA[Get All Employee Mobiles]
    W --> BB[Get Department Employee Mobiles]
    X --> CC[Get Designation Employee Mobiles]
    Y --> DD[Process Custom Mobile List]
    Z --> EE[Get Business Group Mobiles]
    
    AA --> FF[Validate Mobile Numbers]
    BB --> FF
    CC --> FF
    DD --> FF
    EE --> FF
    FF --> GG[Message Content Input]
    GG --> HH[Message Template Selection]
    HH --> II[Personalization Options]
    II --> JJ[Delivery Schedule]
    JJ --> KK{Bulk SMS Validation}
    KK -->|Invalid Recipients| LL[Show Recipient Error]
    LL --> T
    KK -->|Empty Message| MM[Show Message Error]
    MM --> T
    KK -->|Valid Data| NN[Process Bulk SMS]
    NN --> OO[Queue SMS Messages]
    OO --> PP[Send Bulk SMS]
    PP --> QQ[Log Bulk SMS Activity]
    QQ --> RR[Update Delivery Status]
    RR --> SS[Show Bulk Send Report]
    SS --> F
    
    %% SMS Templates Management
    F -->|Manage Templates| TT[Load SMS Templates]
    TT --> UU{Template Action}
    UU -->|Create Template| VV[Create New SMS Template]
    UU -->|Edit Template| WW[Edit Existing Template]
    UU -->|Delete Template| XX[Delete SMS Template]
    UU -->|Copy Template| YY[Copy SMS Template]
    
    VV --> ZZ[Template Name Input]
    WW --> AAA[Load Template for Editing]
    XX --> BBB[Confirm Template Deletion]
    YY --> CCC[Create Template Copy]
    
    ZZ --> DDD[Template Content Input]
    AAA --> EEE[Modify Template Content]
    BBB --> FFF[Remove Template]
    CCC --> GGG[Save Template Copy]
    
    DDD --> HHH[Template Variables Setup]
    EEE --> III[Update Template Variables]
    FFF --> JJJ[Template Deletion Complete]
    GGG --> KKK[Template Copy Complete]
    
    HHH --> LLL[Save New Template]
    III --> MMM[Update Template]
    JJJ --> F
    KKK --> F
    LLL --> F
    MMM --> F
    
    %% SMS Delivery Reports
    F -->|Delivery Reports| NNN[SMS Delivery Reporting]
    NNN --> OOO{Report Type}
    OOO -->|Delivery Status| PPP[Delivery Status Report]
    OOO -->|Failed Messages| QQQ[Failed Messages Report]
    OOO -->|Usage Statistics| RRR[SMS Usage Statistics]
    OOO -->|Monthly Report| SSS[Monthly SMS Report]
    OOO -->|Department Report| TTT[Department-wise SMS Report]
    
    PPP --> UUU[Generate Delivery Report]
    QQQ --> VVV[Generate Failed Messages Report]
    RRR --> WWW[Generate Usage Statistics]
    SSS --> XXX[Generate Monthly Report]
    TTT --> YYY[Generate Department Report]
    
    UUU --> ZZZ[Export SMS Reports]
    VVV --> ZZZ
    WWW --> ZZZ
    XXX --> ZZZ
    YYY --> ZZZ
    ZZZ --> F
    
    %% SMS Configuration
    F -->|SMS Configuration| AAAA[SMS Configuration Management]
    AAAA --> BBBB{Configuration Type}
    BBBB -->|Gateway Settings| CCCC[SMS Gateway Configuration]
    BBBB -->|Message Limits| DDDD[SMS Limit Configuration]
    BBBB -->|Templates| EEEE[Default Template Configuration]
    BBBB -->|Delivery Settings| FFFF[Delivery Configuration]
    
    CCCC --> GGGG[Gateway Provider Settings]
    DDDD --> HHHH[Daily/Monthly Limits]
    EEEE --> IIII[System Template Setup]
    FFFF --> JJJJ[Retry & Timeout Settings]
    
    GGGG --> KKKK[Save Gateway Configuration]
    HHHH --> LLLL[Save Limit Configuration]
    IIII --> MMMM[Save Template Configuration]
    JJJJ --> NNNN[Save Delivery Configuration]
    
    KKKK --> F
    LLLL --> F
    MMMM --> F
    NNNN --> F
    
    %% SMS History and Tracking
    F -->|SMS History| OOOO[SMS History Management]
    OOOO --> PPPP[Display SMS History]
    PPPP --> QQQQ{History Filter}
    QQQQ -->|By Date| RRRR[Filter by Date Range]
    QQQQ -->|By Recipient| SSSS[Filter by Recipient]
    QQQQ -->|By Status| TTTT[Filter by Delivery Status]
    QQQQ -->|By Type| UUUU[Filter by Message Type]
    
    RRRR --> VVVV[Show Date-filtered History]
    SSSS --> WWWW[Show Recipient-filtered History]
    TTTT --> XXXX[Show Status-filtered History]
    UUUU --> YYYY[Show Type-filtered History]
    
    VVVV --> ZZZZ[Display Filtered Results]
    WWWW --> ZZZZ
    XXXX --> ZZZZ
    YYYY --> ZZZZ
    ZZZZ --> AAAAA{History Action}
    AAAAA -->|View Details| BBBBB[View SMS Details]
    AAAAA -->|Resend| CCCCC[Resend Failed SMS]
    AAAAA -->|Export| DDDDD[Export History Data]
    
    BBBBB --> F
    CCCCC --> EEEEE[Process SMS Resend]
    DDDDD --> FFFFF[Generate History Export]
    EEEEE --> F
    FFFFF --> F
    
    %% SMS Scheduling
    F -->|Schedule SMS| GGGGG[SMS Scheduling Interface]
    GGGGG --> HHHHH[Select Recipients]
    HHHHH --> IIIII[Compose Message]
    IIIII --> JJJJJ[Set Schedule Date/Time]
    JJJJJ --> KKKKK[Schedule Frequency]
    KKKKK --> LLLLL{Schedule Type}
    LLLLL -->|One-time| MMMMM[One-time Schedule]
    LLLLL -->|Recurring| NNNNN[Recurring Schedule]
    
    MMMMM --> OOOOO[Set Single Send Time]
    NNNNN --> PPPPP[Set Recurrence Pattern]
    
    OOOOO --> QQQQQ[Save Scheduled SMS]
    PPPPP --> QQQQQ
    QQQQQ --> RRRRR[Add to SMS Queue]
    RRRRR --> SSSSS[Schedule Confirmation]
    SSSSS --> F
    
    %% Business Rules
    TTTTT[Business Rules] --> UUUUU[Valid mobile number required]
    UUUUU --> VVVVV[Message content required]
    VVVVV --> WWWWW[Message length limits]
    WWWWW --> XXXXX[Daily SMS limits per user]
    XXXXX --> YYYYY[Bulk SMS approval required]
    YYYYY --> ZZZZZ[Template validation required]
    ZZZZZ --> M
    
    %% Django Implementation
    AAAAAA[Django Implementation] --> BBBBBB[SMS Model]
    BBBBBB --> CCCCCC[SMSTemplate Model]
    CCCCCC --> DDDDDD[SMSQueue Model]
    DDDDDD --> EEEEEE[SMSDeliveryLog Model]
    EEEEEE --> FFFFFF[SMSForm Validation]
    FFFFFF --> GGGGGG[SMS Management Views]
    GGGGGG --> HHHHHH[SMS Template Views]
    HHHHHH --> IIIIII[SMS Reporting Views]
    IIIIII --> JJJJJJ[SAP Fiori UI Templates]
    JJJJJJ --> KKKKKK[HTMX Dynamic Updates]
    KKKKKK --> F
    
    %% Integration Points
    LLLLLL[Integration Points] --> MMMMMM[Employee Management]
    MMMMMM --> NNNNNN[Corporate Mobile System]
    NNNNNN --> OOOOOO[SMS Gateway API]
    OOOOOO --> PPPPPP[Notification System]
    PPPPPP --> QQQQQQ[Event Management]
    QQQQQQ --> RRRRRR[Emergency Communication]
    RRRRRR --> D
    
    %% SMS Gateway Integration
    SSSSSS[SMS Gateway] --> TTTTTT[Gateway API Integration]
    TTTTTT --> UUUUUU[Message Queue Processing]
    UUUUUU --> VVVVVV[Delivery Status Tracking]
    VVVVVV --> WWWWWW[Failed Message Handling]
    WWWWWW --> XXXXXX[Retry Mechanism]
    XXXXXX --> P
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style P fill:#e8f5e8
    style NN fill:#e8f5e8
    style LLL fill:#e8f5e8
    style MMM fill:#e8f5e8
    style FFF fill:#ffebee
    style N fill:#fff3e0
    style O fill:#fff3e0
    style LL fill:#fff3e0
    style MM fill:#fff3e0
    style UUU fill:#fff3e0
    style VVV fill:#fff3e0
    style WWW fill:#fff3e0
    style XXX fill:#fff3e0
    style YYY fill:#fff3e0
    style KKKK fill:#e8f5e8
    style LLLL fill:#e8f5e8
    style MMMM fill:#e8f5e8
    style NNNN fill:#e8f5e8
    style EEEEE fill:#e8f5e8
    style FFFFF fill:#e8f5e8
    style QQQQQ fill:#e8f5e8
    style AAAAAA fill:#f1f8e9
    style TTTTT fill:#e3f2fd
    style LLLLLL fill:#e0f2f1
    style SSSSSS fill:#f3e5f5
