flowchart TD
    A[User Access Grade Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Grade Master Page]
    
    D --> E[Display Grade Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_Grade]
    G --> H[Display Grid with Pagination]
    H --> I[Show Description & Symbol]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New Grade
    F -->|Add New| K[Show Add Form in Footer]
    K --> L[User Enters Description & Symbol]
    L --> M{Validation Check}
    M -->|Empty Fields| N[Show Required Field Error]
    N --> L
    M -->|Valid Data| O[Check Description Uniqueness]
    O --> P{Description Exists?}
    P -->|Yes| Q[Show 'Grade Already Exists' Alert]
    Q --> L
    P -->|No| R[Check Symbol Uniqueness]
    R --> S{Symbol Exists?}
    S -->|Yes| T[Show 'Symbol Already Exists' Alert]
    T --> L
    S -->|No| U[Insert into tblHR_Grade]
    U --> V{Insert Success?}
    V -->|Success| W[Show 'Record Inserted' Message]
    V -->|Error| X[Show Error Message]
    W --> Y[Refresh Grid Display]
    X --> Y
    Y --> F
    
    %% Edit Grade
    F -->|Edit Record| Z[Enable Edit Mode for Row]
    Z --> AA[User Modifies Description/Symbol]
    AA --> BB{Update Validation}
    BB -->|Invalid| CC[Show Validation Error]
    CC --> AA
    BB -->|Valid| DD[Confirm Update Action]
    DD --> EE{User Confirms?}
    EE -->|No| FF[Cancel Edit Mode]
    FF --> F
    EE -->|Yes| GG[Update tblHR_Grade Record]
    GG --> HH{Update Success?}
    HH -->|Success| II[Show 'Record Updated' Message]
    HH -->|Error| JJ[Show Update Error]
    II --> KK[Refresh Grid Display]
    JJ --> KK
    KK --> F
    
    %% Delete Grade
    F -->|Delete Record| LL[Show Delete Confirmation]
    LL --> MM{User Confirms Delete?}
    MM -->|No| F
    MM -->|Yes| NN[Check Grade Usage]
    NN --> OO{Grade in Use?}
    OO -->|Yes| PP[Show 'Cannot Delete - In Use' Error]
    PP --> F
    OO -->|No| QQ[Delete from tblHR_Grade]
    QQ --> RR{Delete Success?}
    RR -->|Success| SS[Show 'Record Deleted' Message]
    RR -->|Error| TT[Show Delete Error]
    SS --> UU[Refresh Grid Display]
    TT --> UU
    UU --> F
    
    %% Grade Configuration
    F -->|Configure Grade| VV[Load Grade Configuration]
    VV --> WW[Salary Range Setup]
    WW --> XX[Minimum Salary Definition]
    XX --> YY[Maximum Salary Definition]
    YY --> ZZ[Grade Benefits Configuration]
    ZZ --> AAA[Allowance Eligibility]
    AAA --> BBB[Leave Entitlement]
    BBB --> CCC[Bonus Eligibility]
    CCC --> DDD[Save Grade Configuration]
    DDD --> F
    
    %% Grade Progression Rules
    F -->|Progression Rules| EEE[Load Grade Progression]
    EEE --> FFF[Define Promotion Criteria]
    FFF --> GGG[Experience Requirements]
    GGG --> HHH[Performance Standards]
    HHH --> III[Skill Requirements]
    III --> JJJ[Time in Grade Rules]
    JJJ --> KKK[Save Progression Rules]
    KKK --> F
    
    %% Business Rules
    LLL[Business Rules] --> MMM[Description must be unique]
    MMM --> NNN[Symbol must be unique]
    NNN --> OOO[Description is required]
    OOO --> PPP[Symbol is required]
    PPP --> QQQ[Description minimum 3 characters]
    QQQ --> RRR[Symbol maximum 5 characters]
    RRR --> SSS[Grade hierarchy validation]
    SSS --> M
    
    %% Usage Validation
    TTT[Usage Validation] --> UUU[Check Employee Records]
    UUU --> VVV[Check Offer Letters]
    VVV --> WWW[Check Salary Records]
    WWW --> XXX[Check Increment Records]
    XXX --> YYY[Check Promotion Records]
    YYY --> NN
    
    %% Django Implementation
    ZZZ[Django Implementation] --> AAAA[Grade Model]
    AAAA --> BBBB[GradeForm Validation]
    BBBB --> CCCC[Grade CRUD Views]
    CCCC --> DDDD[Grade Configuration Views]
    DDDD --> EEEE[Progression Management Views]
    EEEE --> FFFF[SAP Fiori UI Templates]
    FFFF --> GGGG[HTMX Dynamic Updates]
    GGGG --> F
    
    %% Integration Points
    HHHH[Integration Points] --> IIII[Employee Management]
    IIII --> JJJJ[Payroll Processing]
    JJJJ --> KKKK[Performance Management]
    KKKK --> LLLL[Promotion Workflow]
    LLLL --> MMMM[Benefits Administration]
    MMMM --> NNNN[Leave Management]
    NNNN --> D
    
    %% Salary Band Management
    OOOO[Salary Band Management] --> PPPP[Grade-wise Salary Bands]
    PPPP --> QQQQ[Market Rate Analysis]
    QQQQ --> RRRR[Salary Survey Integration]
    RRRR --> SSSS[Compensation Planning]
    SSSS --> TTTT[Pay Equity Analysis]
    TTTT --> VV
    
    %% Performance Integration
    UUUU[Performance Integration] --> VVVV[Grade-based KPIs]
    VVVV --> WWWW[Performance Standards]
    WWWW --> XXXX[Appraisal Criteria]
    XXXX --> YYYY[Rating Scales]
    YYYY --> ZZZZ[Performance-Pay Link]
    ZZZZ --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style U fill:#e8f5e8
    style GG fill:#e8f5e8
    style QQ fill:#ffebee
    style Q fill:#fff3e0
    style T fill:#fff3e0
    style N fill:#fff3e0
    style VV fill:#e8f5e8
    style EEE fill:#e8f5e8
    style ZZZ fill:#f1f8e9
    style LLL fill:#e3f2fd
    style TTT fill:#e8f5e8
    style HHHH fill:#e0f2f1
    style OOOO fill:#f3e5f5
    style UUUU fill:#fff8e1
