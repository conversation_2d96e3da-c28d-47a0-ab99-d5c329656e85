flowchart TD
    A[User Access Business Group Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Business Group Master Page]
    
    D --> E[Display Business Group Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query BusinessGroup Table]
    G --> H[Display Grid with Pagination]
    H --> I[Show Name, Symbol, Incharge]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New Business Group
    F -->|Add New| K[Show Add Form in Footer]
    K --> L[User Enters Name, Symbol, Incharge]
    L --> M{Validation Check}
    M -->|Empty Fields| N[Show Required Field Error]
    N --> L
    M -->|Valid Data| O[Check Name Uniqueness]
    O --> P{Name Exists?}
    P -->|Yes| Q[Show 'Business Group Already Exists' Alert]
    Q --> L
    P -->|No| R[Check Symbol Uniqueness]
    R --> S{Symbol Exists?}
    S -->|Yes| T[Show 'Symbol Already Exists' Alert]
    T --> L
    S -->|No| U[Insert into BusinessGroup]
    U --> V{Insert Success?}
    V -->|Success| W[Show 'Record Inserted' Message]
    V -->|Error| X[Show Error Message]
    W --> Y[Refresh Grid Display]
    X --> Y
    Y --> F
    
    %% Edit Business Group
    F -->|Edit Record| Z[Enable Edit Mode for Row]
    Z --> AA[User Modifies Name/Symbol/Incharge]
    AA --> BB{Update Validation}
    BB -->|Invalid| CC[Show Validation Error]
    CC --> AA
    BB -->|Valid| DD[Confirm Update Action]
    DD --> EE{User Confirms?}
    EE -->|No| FF[Cancel Edit Mode]
    FF --> F
    EE -->|Yes| GG[Update BusinessGroup Record]
    GG --> HH{Update Success?}
    HH -->|Success| II[Show 'Record Updated' Message]
    HH -->|Error| JJ[Show Update Error]
    II --> KK[Refresh Grid Display]
    JJ --> KK
    KK --> F
    
    %% Delete Business Group
    F -->|Delete Record| LL[Show Delete Confirmation]
    LL --> MM{User Confirms Delete?}
    MM -->|No| F
    MM -->|Yes| NN[Check Business Group Usage]
    NN --> OO{BG in Use?}
    OO -->|Yes| PP[Show 'Cannot Delete - In Use' Error]
    PP --> F
    OO -->|No| QQ[Delete from BusinessGroup]
    QQ --> RR{Delete Success?}
    RR -->|Success| SS[Show 'Record Deleted' Message]
    RR -->|Error| TT[Show Delete Error]
    SS --> UU[Refresh Grid Display]
    TT --> UU
    UU --> F
    
    %% Business Rules
    VV[Business Rules] --> WW[Name must be unique]
    WW --> XX[Symbol must be unique]
    XX --> YY[Incharge is required]
    YY --> ZZ[Name minimum 3 characters]
    ZZ --> AAA[Symbol maximum 10 characters]
    AAA --> M
    
    %% Usage Validation
    BBB[Usage Validation] --> CCC[Check Employee Records]
    CCC --> DDD[Check Budget Allocations]
    DDD --> EEE[Check Work Orders]
    EEE --> FFF[Check Project Assignments]
    FFF --> NN
    
    %% Django Implementation
    GGG[Django Implementation] --> HHH[BusinessGroup Model]
    HHH --> III[BusinessGroupForm Validation]
    III --> JJJ[BusinessGroup CRUD Views]
    JJJ --> KKK[SAP Fiori UI Templates]
    KKK --> LLL[HTMX Dynamic Updates]
    LLL --> F
    
    %% Integration Points
    MMM[Integration Points] --> NNN[Employee Assignment]
    NNN --> OOO[Budget Distribution]
    OOO --> PPP[Project Management]
    PPP --> QQQ[Work Order Assignment]
    QQQ --> RRR[Payroll Processing]
    RRR --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style U fill:#e8f5e8
    style GG fill:#e8f5e8
    style QQ fill:#ffebee
    style Q fill:#fff3e0
    style T fill:#fff3e0
    style N fill:#fff3e0
    style GGG fill:#f1f8e9
    style VV fill:#e3f2fd
    style BBB fill:#e8f5e8
    style MMM fill:#e0f2f1
