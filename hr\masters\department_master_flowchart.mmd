flowchart TD
    A[User Access HR Department Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Department Master Page]
    
    D --> E[Display Department Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_Departments]
    G --> H[Display Grid with Pagination]
    H --> I[Show Description & Symbol]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New Department
    F -->|Add New| K[Show Add Form in Footer]
    K --> L[User Enters Description & Symbol]
    L --> M{Validation Check}
    M -->|Empty Fields| N[Show Required Field Error]
    N --> L
    M -->|Valid Data| O[Check Description Uniqueness]
    O --> P{Description Exists?}
    P -->|Yes| Q[Show 'Department Already Exists' Alert]
    Q --> L
    P -->|No| R[Check Symbol Uniqueness]
    R --> S{Symbol Exists?}
    S -->|Yes| T[Show 'Symbol Already Exists' Alert]
    T --> L
    S -->|No| U[Insert into tblHR_Departments]
    U --> V{Insert Success?}
    V -->|Success| W[Show 'Record Inserted' Message]
    V -->|Error| X[Show Error Message]
    W --> Y[Refresh Grid Display]
    X --> Y
    Y --> F
    
    %% Edit Department
    F -->|Edit Record| Z[Enable Edit Mode for Row]
    Z --> AA[User Modifies Description/Symbol]
    AA --> BB{Update Validation}
    BB -->|Invalid| CC[Show Validation Error]
    CC --> AA
    BB -->|Valid| DD[Confirm Update Action]
    DD --> EE{User Confirms?}
    EE -->|No| FF[Cancel Edit Mode]
    FF --> F
    EE -->|Yes| GG[Update tblHR_Departments Record]
    GG --> HH{Update Success?}
    HH -->|Success| II[Show 'Record Updated' Message]
    HH -->|Error| JJ[Show Update Error]
    II --> KK[Refresh Grid Display]
    JJ --> KK
    KK --> F
    
    %% Delete Department
    F -->|Delete Record| LL[Show Delete Confirmation]
    LL --> MM{User Confirms Delete?}
    MM -->|No| F
    MM -->|Yes| NN[Check Department Usage]
    NN --> OO{Department in Use?}
    OO -->|Yes| PP[Show 'Cannot Delete - In Use' Error]
    PP --> F
    OO -->|No| QQ[Delete from tblHR_Departments]
    QQ --> RR{Delete Success?}
    RR -->|Success| SS[Show 'Record Deleted' Message]
    RR -->|Error| TT[Show Delete Error]
    SS --> UU[Refresh Grid Display]
    TT --> UU
    UU --> F
    
    %% Pagination and Navigation
    F -->|Page Navigation| VV[Change Page Index]
    VV --> WW[Load Next/Previous Page]
    WW --> G
    
    %% Empty Data Template
    F -->|No Data| XX[Show Empty Data Template]
    XX --> YY[Display Add Form]
    YY --> ZZ[User Enters First Department]
    ZZ --> AAA[Insert First Record]
    AAA --> BBB[Initialize Grid]
    BBB --> F
    
    %% Django Implementation Flow
    F -->|Django Path| CCC[Django Department Model]
    CCC --> DDD[DepartmentForm Validation]
    DDD --> EEE[DepartmentListView/CreateView/UpdateView]
    EEE --> FFF[Template Rendering with SAP Fiori UI]
    FFF --> GGG[HTMX Dynamic Updates]
    GGG --> HHH[Company Context Integration]
    HHH --> F
    
    %% Database Schema
    III[Database: tblHR_Departments] --> JJJ[Fields: Id, Description, Symbol]
    JJJ --> KKK[Constraints: Description & Symbol Uniqueness]
    KKK --> LLL[Primary Key: Id (AutoField)]
    LLL --> G
    
    %% Business Rules
    MMM[Business Rules] --> NNN[Description must be unique]
    NNN --> OOO[Symbol must be unique]
    OOO --> PPP[Description is required]
    PPP --> QQQ[Symbol is required]
    QQQ --> RRR[Description minimum 3 characters]
    RRR --> SSS[Symbol maximum 10 characters]
    SSS --> M
    
    %% Department Usage Validation
    TTT[Usage Validation] --> UUU[Check Employee Records]
    UUU --> VVV[Check Budget Allocations]
    VVV --> WWW[Check Work Orders]
    WWW --> XXX[Check Asset Assignments]
    XXX --> YYY[Check Intercom Extensions]
    YYY --> NN
    
    %% Error Handling
    ZZZ[Error Handling] --> AAAA[Client-side Validation]
    AAAA --> BBBB[Server-side Validation]
    BBBB --> CCCC[Database Constraint Checks]
    CCCC --> DDDD[User-friendly Error Messages]
    DDDD --> EEEE[Exception Handling]
    EEEE --> M
    
    %% Security & Permissions
    FFFF[Security Layer] --> GGGG[User Authentication Required]
    GGGG --> HHHH[HR Module Access Permission]
    HHHH --> IIII[Master Data Management Rights]
    IIII --> JJJJ[CRUD Operation Authorization]
    JJJJ --> B
    
    %% Integration Points
    KKKK[Integration Points] --> LLLL[Employee Management]
    LLLL --> MMMM[Payroll System]
    MMMM --> NNNN[Budget Allocation]
    NNNN --> OOOO[Asset Management]
    OOOO --> PPPP[Communication Systems]
    PPPP --> QQQQ[Organizational Hierarchy]
    QQQQ --> D
    
    %% Audit Trail
    RRRR[Audit Trail] --> SSSS[Creation Timestamp]
    SSSS --> TTTT[Modification Tracking]
    TTTT --> UUUU[User Action Logging]
    UUUU --> VVVV[Change History]
    VVVV --> WWWW[Data Integrity Monitoring]
    WWWW --> U
    
    %% Master Data Relationships
    XXXX[Master Data Relations] --> YYYY[Department → Employee]
    YYYY --> ZZZZ[Department → Budget]
    ZZZZ --> AAAAA[Department → Assets]
    AAAAA --> BBBBB[Department → Intercom]
    BBBBB --> CCCCC[Department → Work Orders]
    CCCCC --> DDDDD[Hierarchical Structure]
    DDDDD --> D
    
    %% Reporting Integration
    EEEEE[Reporting Integration] --> FFFFF[Department-wise Reports]
    FFFFF --> GGGGG[Employee Distribution]
    GGGGG --> HHHHH[Budget Utilization]
    HHHHH --> IIIII[Asset Allocation]
    IIIII --> JJJJJ[Performance Metrics]
    JJJJJ --> KKKKK[Organizational Analytics]
    KKKKK --> D
    
    %% Data Export/Import
    LLLLL[Data Management] --> MMMMM[Export Department List]
    MMMMM --> NNNNN[Import Department Data]
    NNNNN --> OOOOO[Bulk Operations]
    OOOOO --> PPPPP[Data Validation]
    PPPPP --> QQQQQ[Error Reporting]
    QQQQQ --> F
    
    %% Search and Filter
    RRRRR[Search & Filter] --> SSSSS[Description Search]
    SSSSS --> TTTTT[Symbol Filter]
    TTTTT --> UUUUU[Quick Search]
    UUUUU --> VVVVV[Advanced Filter]
    VVVVV --> WWWWW[Sort Options]
    WWWWW --> F
    
    %% Performance Optimization
    XXXXX[Performance] --> YYYYY[Grid Pagination]
    YYYYY --> ZZZZZ[Lazy Loading]
    ZZZZZ --> AAAAAA[Caching Strategy]
    AAAAAA --> BBBBBB[Query Optimization]
    BBBBBB --> CCCCCC[Index Management]
    CCCCCC --> G
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style U fill:#e8f5e8
    style GG fill:#e8f5e8
    style QQ fill:#ffebee
    style Q fill:#fff3e0
    style T fill:#fff3e0
    style N fill:#fff3e0
    style CCC fill:#f1f8e9
    style III fill:#fafafa
    style MMM fill:#e3f2fd
    style TTT fill:#e8f5e8
    style ZZZ fill:#fce4ec
    style FFFF fill:#f3e5f5
    style KKKK fill:#e0f2f1
    style RRRR fill:#fff8e1
    style XXXX fill:#f1f8e9
    style EEEEE fill:#e3f2fd
    style LLLLL fill:#fff3e0
    style RRRRR fill:#e8f5e8
    style XXXXX fill:#e3f2fd
