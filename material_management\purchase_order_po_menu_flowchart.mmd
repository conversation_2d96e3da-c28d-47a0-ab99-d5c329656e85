graph TD
    A[Purchase Order PO Menu] --> B[Page Load & Initialization]
    A --> C[PO Operations]
    A --> D[PO Management]
    A --> E[PO Workflow]

    %% Page Load & Initialization
    B --> B1[Load PO Dashboard]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Setup Navigation Menu]
    B3 --> B4[Load PO Grid]
    B4 --> B5[Apply Company Filter]
    B5 --> B6[Apply Financial Year Filter]
    B6 --> B7[Display PO List]
    B7 --> B8[Setup Action Buttons]
    B8 --> B9[Enable Search Functionality]
    B9 --> B10[Page Ready for User Interaction]

    %% PO Operations
    C --> C1[Create New PO]
    C --> C2[Edit PO]
    C --> C3[Delete PO]
    C --> C4[View PO Details]
    C --> C5[Print PO]

    %% Create New PO
    C1 --> C1a[Navigate to New PO Form]
    C1a --> C1b[PO Creation Method Selection]
    C1b --> C1c{PO Creation Method}
    C1c -->|From PR| C1d[Select PR for PO]
    C1c -->|From SPR| C1e[Select SPR for PO]
    C1c -->|Direct PO| C1f[Direct PO Creation]
    
    %% From PR Path
    C1d --> C1g[Load Approved PRs]
    C1g --> C1h[Select PR Items]
    C1h --> C1i[Load PR Item Details]
    C1i --> C1j[Supplier Selection for Items]
    C1j --> C1k[Rate Negotiation]
    C1k --> C1l[PO Header Creation]
    
    %% From SPR Path
    C1e --> C1m[Load Authorized SPRs]
    C1m --> C1n[Select SPR Items]
    C1n --> C1o[Load SPR Item Details]
    C1o --> C1p[Supplier Selection for Items]
    C1p --> C1q[Rate Negotiation]
    C1q --> C1l
    
    %% Direct PO Path
    C1f --> C1r[Manual Item Selection]
    C1r --> C1s[Item Master Browse]
    C1s --> C1t[Select Items]
    C1t --> C1u[Supplier Selection]
    C1u --> C1v[Rate Entry]
    C1v --> C1l
    
    %% PO Header Creation
    C1l --> C1w[PO Number Generation]
    C1w --> C1x[PO Date Entry]
    C1x --> C1y[Supplier Selection]
    C1y --> C1z[Delivery Address]
    C1z --> C1aa[Payment Terms]
    C1aa --> C1bb[Delivery Terms]
    C1bb --> C1cc[Validity Period]
    C1cc --> C1dd[Special Instructions]
    C1dd --> C1ee[PO Items Section]
    
    C1ee --> C1ff[Item Details Verification]
    C1ff --> C1gg[Quantity Confirmation]
    C1gg --> C1hh[Rate Confirmation]
    C1hh --> C1ii[Delivery Date Setting]
    C1ii --> C1jj[Tax Calculations]
    C1jj --> C1kk[Total Amount Calculation]
    C1kk --> C1ll[PO Validation]
    C1ll --> C1mm{Validation Passed?}
    C1mm -->|No| C1nn[Show Validation Errors]
    C1mm -->|Yes| C1oo[Save PO Master]
    
    C1nn --> C1l
    C1oo --> C1pp[Generate PO Number]
    C1pp --> C1qq[Set System Information]
    C1qq --> C1rr[Set Company ID]
    C1rr --> C1ss[Set Session Details]
    C1ss --> C1tt[Set Financial Year]
    C1tt --> C1uu[Set PO Status - Draft]
    C1uu --> C1vv[Execute Database Insert]
    C1vv --> C1ww{Insert Successful?}
    C1ww -->|No| C1xx[Database Error Message]
    C1ww -->|Yes| C1yy[Save PO Items]
    
    C1xx --> C1l
    C1yy --> C1zz[Loop Through PO Items]
    C1zz --> C1aaa[Create PO Detail Record]
    C1aaa --> C1bbb[Calculate Item Totals]
    C1bbb --> C1ccc{More Items?}
    C1ccc -->|Yes| C1zz
    C1ccc -->|No| C1ddd[Update PO Totals]
    
    C1ddd --> C1eee[PO Creation Success]
    C1eee --> C1fff[Clear Form]
    C1fff --> C1ggg[Refresh PO Grid]
    C1ggg --> C1hhh[Return to PO Dashboard]

    %% Edit PO
    C2 --> C2a[Select PO for Edit]
    C2a --> C2b[Check PO Status]
    C2b --> C2c{PO Editable?}
    C2c -->|No| C2d[PO Cannot be Edited]
    C2c -->|Yes| C2e[Navigate to Edit Form]
    
    C2d --> B7
    C2e --> C2f[Load PO Data]
    C2f --> C2g[Pre-populate Header Fields]
    C2g --> C2h[Load PO Items]
    C2h --> C2i[Display Items Grid]
    C2i --> C2j[Enable Field Modifications]
    C2j --> C2k[Update Header Information]
    C2k --> C2l[Modify PO Items]
    C2l --> C2m[Add New Items]
    C2m --> C2n[Remove Items]
    C2n --> C2o[Update Item Details]
    C2o --> C2p[Recalculate Totals]
    C2p --> C2q[Form Validation]
    C2q --> C2r{Validation Passed?}
    C2r -->|No| C2s[Show Validation Errors]
    C2r -->|Yes| C2t[Update PO Record]
    
    C2s --> C2k
    C2t --> C2u[Set Update Information]
    C2u --> C2v[Set Modified Date/Time]
    C2v --> C2w[Set Modified By User]
    C2w --> C2x[Execute Database Update]
    C2x --> C2y{Update Successful?}
    C2y -->|No| C2z[Update Error Message]
    C2y -->|Yes| C2aa[Update Success Message]
    
    C2z --> C2k
    C2aa --> C2bb[Refresh PO Grid]
    C2bb --> C2cc[Return to PO Dashboard]

    %% Delete PO
    C3 --> C3a[Select PO for Delete]
    C3a --> C3b[Check PO Status]
    C3b --> C3c{PO Deletable?}
    C3c -->|No| C3d[PO Cannot be Deleted]
    C3c -->|Yes| C3e[Navigate to Delete Confirmation]
    
    C3d --> B7
    C3e --> C3f[Load PO Details]
    C3f --> C3g[Display PO Information]
    C3g --> C3h[Show Delete Warning]
    C3h --> C3i[Confirm Deletion Dialog]
    C3i --> C3j{Confirm Delete?}
    C3j -->|No| C3k[Cancel Deletion]
    C3j -->|Yes| C3l[Check Dependencies]
    
    C3k --> B7
    C3l --> C3m[Check GRN References]
    C3m --> C3n[Check Invoice References]
    C3n --> C3o[Check Approval Status]
    C3o --> C3p{Has Dependencies?}
    C3p -->|Yes| C3q[Cannot Delete - Dependencies Exist]
    C3p -->|No| C3r[Execute Delete Operation]
    
    C3q --> B7
    C3r --> C3s[Delete PO Items]
    C3s --> C3t[Delete PO Master]
    C3t --> C3u{Delete Successful?}
    C3u -->|No| C3v[Delete Error Message]
    C3u -->|Yes| C3w[Delete Success Message]
    
    C3v --> B7
    C3w --> C3x[Refresh PO Grid]
    C3x --> C3y[Update PO Count]
    C3y --> B7

    %% View PO Details
    C4 --> C4a[Select PO for View]
    C4a --> C4b[Navigate to Details View]
    C4b --> C4c[Load Complete PO Data]
    C4c --> C4d[Display Header Information]
    C4d --> C4e[Display Supplier Details]
    C4e --> C4f[Display PO Items]
    C4f --> C4g[Display Terms & Conditions]
    C4g --> C4h[Display Approval History]
    C4h --> C4i[Display Status Information]
    C4i --> C4j[Display Related Documents]
    C4j --> C4k[Show Action Buttons]
    C4k --> C4l[Edit Option]
    C4l --> C4m[Delete Option]
    C4m --> C4n[Print Option]
    C4n --> C4o[Approve Option]

    %% Print PO
    C5 --> C5a[Select PO for Print]
    C5a --> C5b[Load Print Template]
    C5b --> C5c[Format PO Data]
    C5c --> C5d[Include Header Information]
    C5d --> C5e[Include Supplier Details]
    C5e --> C5f[Include PO Items]
    C5f --> C5g[Include Terms & Conditions]
    C5g --> C5h[Include Tax Calculations]
    C5h --> C5i[Include Total Amounts]
    C5i --> C5j[Include Approval Details]
    C5j --> C5k[Generate Print Preview]
    C5k --> C5l[Print Document]
    C5l --> C5m[Save Print Log]

    %% PO Management
    D --> D1[PO Search & Filter]
    D --> D2[PO List Management]
    D --> D3[PO Status Management]
    D --> D4[Supplier Management]

    %% PO Search & Filter
    D1 --> D1a[Search Interface]
    D1a --> D1b[PO Number Search]
    D1b --> D1c[Date Range Filter]
    D1c --> D1d[Supplier Filter]
    D1d --> D1e[Status Filter]
    D1e --> D1f[Amount Range Filter]
    D1f --> D1g[Item Filter]
    D1g --> D1h[Apply Search Filters]
    D1h --> D1i[Execute Search Query]
    D1i --> D1j[Display Filtered Results]
    D1j --> D1k[Update Result Count]
    D1k --> D1l[Enable Export Options]

    %% PO List Management
    D2 --> D2a[Grid Display Options]
    D2a --> D2b[Column Selection]
    D2b --> D2c[Sorting Options]
    D2c --> D2d[Pagination Settings]
    D2d --> D2e[Row Selection]
    D2e --> D2f[Bulk Action Selection]
    D2f --> D2g[Grid Refresh]
    D2g --> D2h[Export Grid Data]

    %% PO Status Management
    D3 --> D3a[Status Tracking]
    D3a --> D3b[Draft POs]
    D3b --> D3c[Submitted POs]
    D3c --> D3d[Approved POs]
    D3d --> D3e[Authorized POs]
    D3e --> D3f[Rejected POs]
    D3f --> D3g[Completed POs]
    D3g --> D3h[Status Change Options]
    D3h --> D3i[Status Update Workflow]

    %% Supplier Management
    D4 --> D4a[Supplier Performance]
    D4a --> D4b[Delivery Performance]
    D4b --> D4c[Quality Performance]
    D4c --> D4d[Price Comparison]
    D4d --> D4e[Supplier Rating]
    D4e --> D4f[Supplier Reports]

    %% PO Workflow
    E --> E1[PO Approval Workflow]
    E --> E2[PO Authorization Workflow]
    E --> E3[PO Notification System]
    E --> E4[PO Integration]

    %% PO Approval Workflow
    E1 --> E1a[Submit for Approval]
    E1a --> E1b[Route to Approver]
    E1b --> E1c[Approval Review]
    E1c --> E1d[Check Budget Approval]
    E1d --> E1e[Check Technical Approval]
    E1e --> E1f[Approve/Reject Decision]
    E1f --> E1g{Approved?}
    E1g -->|Yes| E1h[Update Status - Approved]
    E1g -->|No| E1i[Update Status - Rejected]
    
    E1h --> E1j[Send Approval Notification]
    E1i --> E1k[Send Rejection Notification]
    E1j --> E1l[Route to Authorization]
    E1k --> E1m[Return to Creator]

    %% PO Authorization Workflow
    E2 --> E2a[Submit for Authorization]
    E2a --> E2b[Route to Authorizer]
    E2b --> E2c[Authorization Review]
    E2c --> E2d[Final Authority Check]
    E2d --> E2e[Authorize/Reject Decision]
    E2e --> E2f{Authorized?}
    E2f -->|Yes| E2g[Update Status - Authorized]
    E2f -->|No| E2h[Update Status - Rejected]
    
    E2g --> E2i[Send Authorization Notification]
    E2h --> E2j[Send Rejection Notification]
    E2i --> E2k[Send PO to Supplier]
    E2j --> E2l[Return to Previous Level]

    %% PO Notification System
    E3 --> E3a[Email Notifications]
    E3a --> E3b[SMS Alerts]
    E3b --> E3c[Dashboard Notifications]
    E3c --> E3d[Mobile Push Notifications]
    E3d --> E3e[Supplier Notifications]
    E3e --> E3f[Escalation Notifications]

    %% PO Integration
    E4 --> E4a[GRN Integration]
    E4a --> E4b[Invoice Integration]
    E4b --> E4c[Inventory Integration]
    E4c --> E4d[Accounts Integration]
    E4d --> E4e[Budget Integration]
    E4e --> E4f[Supplier Portal Integration]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef operationClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef managementClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef workflowClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B10 initClass
    class C,C1,C2,C3,C4,C5 operationClass
    class D,D1,D2,D3,D4 managementClass
    class E,E1,E2,E3,E4 workflowClass
    class C1c,C1mm,C1ww,C1ccc,C2c,C2r,C2y,C3c,C3j,C3p,C3u,E1g,E2f decisionClass
    class C1nn,C1xx,C2d,C2s,C2z,C3d,C3q,C3v errorClass
    class C1eee,C2aa,C3w successClass
