flowchart TD
    A[User Access Gate Pass Types Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Gate Pass Types Master Page]
    
    D --> E[Display Gate Pass Types Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblGatePass_Reason]
    G --> H[Display Grid with Pagination]
    H --> I[Show Reason, WONo, Enquiry, Other]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New Gate Pass Type
    F -->|Add New Type| K[Show Add Gate Pass Type Form]
    K --> L[User Enters Type Details]
    L --> M[Reason Description Input]
    M --> N[Work Order Number Input]
    N --> O[Enquiry Number Input]
    O --> P[Other Details Input]
    P --> Q{Validation Check}
    Q -->|Empty Reason| R[Show Required Field Error]
    R --> L
    Q -->|Valid Data| S[Check Reason Uniqueness]
    S --> T{Reason Exists?}
    T -->|Yes| U[Show 'Gate Pass Type Already Exists' Alert]
    U --> L
    T -->|No| V[Insert into tblGatePass_Reason]
    V --> W{Insert Success?}
    W -->|Success| X[Show 'Gate Pass Type Added' Message]
    W -->|Error| Y[Show Error Message]
    X --> Z[Refresh Grid Display]
    Y --> Z
    Z --> F
    
    %% Edit Gate Pass Type
    F -->|Edit Record| AA[Enable Edit Mode for Row]
    AA --> BB[User Modifies Type Details]
    BB --> CC{Update Validation}
    CC -->|Invalid| DD[Show Validation Error]
    DD --> BB
    CC -->|Valid| EE[Confirm Update Action]
    EE --> FF{User Confirms?}
    FF -->|No| GG[Cancel Edit Mode]
    GG --> F
    FF -->|Yes| HH[Update tblGatePass_Reason Record]
    HH --> II{Update Success?}
    II -->|Success| JJ[Show 'Record Updated' Message]
    II -->|Error| KK[Show Update Error]
    JJ --> LL[Refresh Grid Display]
    KK --> LL
    LL --> F
    
    %% Delete Gate Pass Type
    F -->|Delete Record| MM[Show Delete Confirmation]
    MM --> NN{User Confirms Delete?}
    NN -->|No| F
    NN -->|Yes| OO[Check Gate Pass Type Usage]
    OO --> PP{Type in Use?}
    PP -->|Yes| QQ[Show 'Cannot Delete - In Use' Error]
    QQ --> F
    PP -->|No| RR[Delete from tblGatePass_Reason]
    RR --> SS{Delete Success?}
    SS -->|Success| TT[Show 'Record Deleted' Message]
    SS -->|Error| UU[Show Delete Error]
    TT --> VV[Refresh Grid Display]
    UU --> VV
    VV --> F
    
    %% Gate Pass Type Configuration
    F -->|Configure Types| WW[Load Type Configuration]
    WW --> XX{Configuration Type}
    XX -->|Work Order Types| YY[Configure WO-based Gate Pass]
    XX -->|Enquiry Types| ZZ[Configure Enquiry-based Gate Pass]
    XX -->|Personal Types| AAA[Configure Personal Gate Pass]
    XX -->|Emergency Types| BBB[Configure Emergency Gate Pass]
    XX -->|Official Types| CCC[Configure Official Gate Pass]
    
    YY --> DDD[Set Work Order Requirements]
    ZZ --> EEE[Set Enquiry Requirements]
    AAA --> FFF[Set Personal Requirements]
    BBB --> GGG[Set Emergency Requirements]
    CCC --> HHH[Set Official Requirements]
    
    DDD --> III[Define Approval Workflow]
    EEE --> III
    FFF --> III
    GGG --> III
    HHH --> III
    III --> JJJ[Set Duration Limits]
    JJJ --> KKK[Set Authorization Levels]
    KKK --> LLL[Save Configuration]
    LLL --> F
    
    %% Gate Pass Type Templates
    F -->|Manage Templates| MMM[Load Gate Pass Templates]
    MMM --> NNN{Template Action}
    NNN -->|Create Template| OOO[Create New Template]
    NNN -->|Edit Template| PPP[Edit Existing Template]
    NNN -->|Delete Template| QQQ[Delete Template]
    NNN -->|Copy Template| RRR[Copy Template]
    
    OOO --> SSS[Define Template Structure]
    PPP --> TTT[Modify Template Structure]
    QQQ --> UUU[Confirm Template Deletion]
    RRR --> VVV[Create Template Copy]
    
    SSS --> WWW[Set Required Fields]
    TTT --> WWW
    VVV --> WWW
    WWW --> XXX[Set Validation Rules]
    XXX --> YYY[Set Approval Rules]
    YYY --> ZZZ[Save Template]
    ZZZ --> F
    
    %% Gate Pass Type Reports
    F -->|Type Reports| AAAA[Gate Pass Type Reporting]
    AAAA --> BBBB{Report Type}
    BBBB -->|Usage Statistics| CCCC[Type Usage Statistics]
    BBBB -->|Approval Analysis| DDDD[Type Approval Analysis]
    BBBB -->|Duration Analysis| EEEE[Type Duration Analysis]
    BBBB -->|Department Usage| FFFF[Department-wise Type Usage]
    
    CCCC --> GGGG[Generate Usage Report]
    DDDD --> HHHH[Generate Approval Report]
    EEEE --> IIII[Generate Duration Report]
    FFFF --> JJJJ[Generate Department Report]
    
    GGGG --> KKKK[Export Type Reports]
    HHHH --> KKKK
    IIII --> KKKK
    JJJJ --> KKKK
    KKKK --> F
    
    %% Type-based Workflow Rules
    F -->|Workflow Rules| LLLL[Type-based Workflow Management]
    LLLL --> MMMM{Workflow Configuration}
    MMMM -->|Approval Hierarchy| NNNN[Set Type-specific Approvers]
    MMMM -->|Time Limits| OOOO[Set Type-specific Time Limits]
    MMMM -->|Notification Rules| PPPP[Set Type-specific Notifications]
    MMMM -->|Escalation Rules| QQQQ[Set Type-specific Escalations]
    
    NNNN --> RRRR[Configure Multi-level Approval]
    OOOO --> SSSS[Configure Duration Constraints]
    PPPP --> TTTT[Configure Notification Templates]
    QQQQ --> UUUU[Configure Escalation Matrix]
    
    RRRR --> VVVV[Save Workflow Rules]
    SSSS --> VVVV
    TTTT --> VVVV
    UUUU --> VVVV
    VVVV --> F
    
    %% Business Rules
    WWWW[Business Rules] --> XXXX[Reason must be unique]
    XXXX --> YYYY[Reason description required]
    YYYY --> ZZZZ[Type categorization required]
    ZZZZ --> AAAAA[Approval workflow mandatory]
    AAAAA --> BBBBB[Duration limits enforced]
    BBBBB --> CCCCC[Authorization levels defined]
    CCCCC --> Q
    
    %% Django Implementation
    DDDDD[Django Implementation] --> EEEEE[GatePassReason Model]
    EEEEE --> FFFFF[GatePassType Model]
    FFFFF --> GGGGG[GatePassTemplate Model]
    GGGGG --> HHHHH[GatePassTypeForm Validation]
    HHHHH --> IIIII[Type Management Views]
    IIIII --> JJJJJ[Template Management Views]
    JJJJJ --> KKKKK[Workflow Configuration Views]
    KKKKK --> LLLLL[SAP Fiori UI Templates]
    LLLLL --> MMMMM[HTMX Dynamic Updates]
    MMMMM --> F
    
    %% Integration Points
    NNNNN[Integration Points] --> OOOOO[Gate Pass Management]
    OOOOO --> PPPPP[Employee Management]
    PPPPP --> QQQQQ[Work Order System]
    QQQQQ --> RRRRR[Enquiry Management]
    RRRRR --> SSSSS[Approval Workflow]
    SSSSS --> TTTTT[Notification System]
    TTTTT --> D
    
    %% Type Categories
    UUUUU[Type Categories] --> VVVVV[Personal Gate Pass Types]
    VVVVV --> WWWWW[Official Gate Pass Types]
    WWWWW --> XXXXX[Emergency Gate Pass Types]
    XXXXX --> YYYYY[Work Order Gate Pass Types]
    YYYYY --> ZZZZZ[Enquiry Gate Pass Types]
    ZZZZZ --> AAAAAA[Visitor Gate Pass Types]
    AAAAAA --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style V fill:#e8f5e8
    style HH fill:#e8f5e8
    style RR fill:#ffebee
    style LLL fill:#e8f5e8
    style ZZZ fill:#e8f5e8
    style VVVV fill:#e8f5e8
    style U fill:#fff3e0
    style R fill:#fff3e0
    style QQ fill:#fff3e0
    style UUU fill:#fff3e0
    style DDDDD fill:#f1f8e9
    style WWWW fill:#e3f2fd
    style NNNNN fill:#e0f2f1
    style UUUUU fill:#f3e5f5
