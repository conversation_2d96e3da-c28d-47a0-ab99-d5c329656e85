flowchart TD
    A[User Access Corporate Mobile Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Corporate Mobile Master Page]
    
    D --> E[Display Corporate Mobile Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_CoporateMobileNo]
    G --> H[Display Grid with Pagination]
    H --> I[Show Mobile No & Limit Amount]
    I --> J[Show Edit/Delete Links]
    J --> F
    
    %% Add New Corporate Mobile
    F -->|Add New Mobile| K[Show Add Mobile Form]
    K --> L[User Enters Mobile Details]
    L --> M[Mobile Number Input]
    M --> N[Limit Amount Input]
    N --> O{Validation Check}
    O -->|Empty Fields| P[Show Required Field Error]
    P --> L
    O -->|Invalid Mobile| Q[Show Mobile Format Error]
    Q --> L
    O -->|Invalid Amount| R[Show Amount Format Error]
    R --> L
    O -->|Valid Data| S[Check Mobile Number Uniqueness]
    S --> T{Mobile Number Exists?}
    T -->|Yes| U[Show 'Mobile Number Already Exists' Alert]
    U --> L
    T -->|No| V[Insert into tblHR_CoporateMobileNo]
    V --> W{Insert Success?}
    W -->|Success| X[Show 'Mobile Added' Message]
    W -->|Error| Y[Show Error Message]
    X --> Z[Refresh Grid Display]
    Y --> Z
    Z --> F
    
    %% Edit Corporate Mobile
    F -->|Edit Record| AA[Enable Edit Mode for Row]
    AA --> BB[User Modifies Mobile/Limit]
    BB --> CC{Update Validation}
    CC -->|Invalid| DD[Show Validation Error]
    DD --> BB
    CC -->|Valid| EE[Confirm Update Action]
    EE --> FF{User Confirms?}
    FF -->|No| GG[Cancel Edit Mode]
    GG --> F
    FF -->|Yes| HH[Update tblHR_CoporateMobileNo Record]
    HH --> II{Update Success?}
    II -->|Success| JJ[Show 'Record Updated' Message]
    II -->|Error| KK[Show Update Error]
    JJ --> LL[Refresh Grid Display]
    KK --> LL
    LL --> F
    
    %% Delete Corporate Mobile
    F -->|Delete Record| MM[Show Delete Confirmation]
    MM --> NN{User Confirms Delete?}
    NN -->|No| F
    NN -->|Yes| OO[Check Mobile Usage]
    OO --> PP{Mobile in Use?}
    PP -->|Yes| QQ[Show 'Cannot Delete - In Use' Error]
    QQ --> F
    PP -->|No| RR[Delete from tblHR_CoporateMobileNo]
    RR --> SS{Delete Success?}
    SS -->|Success| TT[Show 'Record Deleted' Message]
    SS -->|Error| UU[Show Delete Error]
    TT --> VV[Refresh Grid Display]
    UU --> VV
    VV --> F
    
    %% Mobile Assignment Management
    F -->|Assign Mobile| WW[Load Mobile Assignment]
    WW --> XX[Select Available Mobile]
    XX --> YY[Search Employee]
    YY --> ZZ[Employee Selection]
    ZZ --> AAA[Verify Assignment Eligibility]
    AAA --> BBB{Employee Eligible?}
    BBB -->|No| CCC[Show Eligibility Error]
    CCC --> WW
    BBB -->|Yes| DDD[Check Existing Assignment]
    DDD --> EEE{Employee Has Mobile?}
    EEE -->|Yes| FFF[Show 'Employee Already Has Mobile' Alert]
    FFF --> WW
    EEE -->|No| GGG[Assign Mobile to Employee]
    GGG --> HHH[Set Assignment Date]
    HHH --> III[Record Assignment Details]
    III --> JJJ[Generate Assignment Document]
    JJJ --> KKK[Send Assignment Notification]
    KKK --> LLL[Mobile Assignment Complete]
    LLL --> F
    
    %% Mobile Bill Management
    F -->|Manage Bills| MMM[Load Mobile Bill Management]
    MMM --> NNN[Display Assigned Mobiles]
    NNN --> OOO{Bill Management Action}
    
    OOO -->|Enter Bill| PPP[Mobile Bill Entry Form]
    PPP --> QQQ[Select Mobile Number]
    QQQ --> RRR[Enter Bill Month]
    RRR --> SSS[Enter Bill Amount]
    SSS --> TTT[Enter Tax Amount]
    TTT --> UUU[Bill Validation]
    UUU --> VVV{Bill Valid?}
    VVV -->|No| WWW[Show Bill Validation Error]
    WWW --> PPP
    VVV -->|Yes| XXX[Save Bill Record]
    XXX --> YYY[Insert into tblHR_MobileBill]
    YYY --> ZZZ[Generate Bill Entry Report]
    ZZZ --> AAAA[Send Bill Notification]
    AAAA --> F
    
    OOO -->|Approve Bill| BBBB[Bill Approval Workflow]
    BBBB --> CCCC[Review Bill Details]
    CCCC --> DDDD[Verify Bill Amount]
    DDDD --> EEEE[Check Limit Compliance]
    EEEE --> FFFF{Within Limit?}
    FFFF -->|No| GGGG[Show Limit Exceeded Error]
    GGGG --> BBBB
    FFFF -->|Yes| HHHH[Approve Bill]
    HHHH --> IIII[Update Approval Status]
    IIII --> JJJJ[Generate Approval Document]
    JJJJ --> KKKK[Send Approval Notification]
    KKKK --> F
    
    %% Mobile Usage Reports
    F -->|Usage Reports| LLLL[Mobile Usage Reporting]
    LLLL --> MMMM{Report Type}
    MMMM -->|Monthly Usage| NNNN[Monthly Usage Report]
    MMMM -->|Employee-wise| OOOO[Employee-wise Usage Report]
    MMMM -->|Limit Analysis| PPPP[Limit Analysis Report]
    MMMM -->|Bill Summary| QQQQ[Bill Summary Report]
    
    NNNN --> RRRR[Generate Monthly Report]
    OOOO --> SSSS[Generate Employee Report]
    PPPP --> TTTT[Generate Limit Report]
    QQQQ --> UUUU[Generate Summary Report]
    
    RRRR --> VVVV[Export Usage Reports]
    SSSS --> VVVV
    TTTT --> VVVV
    UUUU --> VVVV
    VVVV --> F
    
    %% Business Rules
    WWWW[Business Rules] --> XXXX[Mobile number must be unique]
    XXXX --> YYYY[Limit amount must be positive]
    YYYY --> ZZZZ[Mobile format validation]
    ZZZZ --> AAAAA[One mobile per employee]
    AAAAA --> BBBBB[Bill amount within limit]
    BBBBB --> CCCCC[Monthly bill entry required]
    CCCCC --> O
    
    %% Django Implementation
    DDDDD[Django Implementation] --> EEEEE[CorporateMobileNumber Model]
    EEEEE --> FFFFF[MobileBill Model]
    FFFFF --> GGGGG[MobileAssignment Model]
    GGGGG --> HHHHH[CorporateMobileForm Validation]
    HHHHH --> IIIII[Mobile Management Views]
    IIIII --> JJJJJ[Bill Management Views]
    JJJJJ --> KKKKK[Usage Reporting Views]
    KKKKK --> LLLLL[SAP Fiori UI Templates]
    LLLLL --> MMMMM[HTMX Dynamic Updates]
    MMMMM --> F
    
    %% Integration Points
    NNNNN[Integration Points] --> OOOOO[Employee Management]
    OOOOO --> PPPPP[Payroll System]
    PPPPP --> QQQQQ[Expense Management]
    QQQQQ --> RRRRR[Approval Workflow]
    RRRRR --> SSSSS[Accounts Integration]
    SSSSS --> TTTTT[Reimbursement Processing]
    TTTTT --> D
    
    %% Cost Management
    UUUUU[Cost Management] --> VVVVV[Budget Allocation]
    VVVVV --> WWWWW[Cost Center Assignment]
    WWWWW --> XXXXX[Expense Tracking]
    XXXXX --> YYYYY[Variance Analysis]
    YYYYY --> ZZZZZ[Cost Optimization]
    ZZZZZ --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style V fill:#e8f5e8
    style HH fill:#e8f5e8
    style RR fill:#ffebee
    style GGG fill:#e8f5e8
    style XXX fill:#e8f5e8
    style HHHH fill:#e8f5e8
    style U fill:#fff3e0
    style P fill:#fff3e0
    style Q fill:#fff3e0
    style R fill:#fff3e0
    style CCC fill:#fff3e0
    style FFF fill:#fff3e0
    style WWW fill:#fff3e0
    style GGGG fill:#fff3e0
    style DDDDD fill:#f1f8e9
    style WWWW fill:#e3f2fd
    style NNNNN fill:#e0f2f1
    style UUUUU fill:#f3e5f5
