graph TD
    A[Purchase Requisition PR Menu] --> B[Page Load & Initialization]
    A --> C[PR Operations]
    A --> D[PR Management]
    A --> E[PR Workflow]

    %% Page Load & Initialization
    B --> B1[Load PR Dashboard]
    B1 --> B2[Initialize Page Components]
    B2 --> B3[Setup Navigation Menu]
    B3 --> B4[Load PR Grid]
    B4 --> B5[Apply Company Filter]
    B5 --> B6[Apply Financial Year Filter]
    B6 --> B7[Display PR List]
    B7 --> B8[Setup Action Buttons]
    B8 --> B9[Enable Search Functionality]
    B9 --> B10[Page Ready for User Interaction]

    %% PR Operations
    C --> C1[Create New PR]
    C --> C2[Edit PR]
    C --> C3[Delete PR]
    C --> C4[View PR Details]
    C --> C5[Print PR]

    %% Create New PR
    C1 --> C1a[Navigate to New PR Form]
    C1a --> C1b[Load PR Creation Form]
    C1b --> C1c[PR Header Information]
    C1c --> C1d[PR Number Generation]
    C1d --> C1e[PR Date Entry]
    C1e --> C1f[Department Selection]
    C1f --> C1g[Requested By Entry]
    C1g --> C1h[Priority Selection]
    C1h --> C1i[Required Date Entry]
    C1i --> C1j[Purpose/Remarks Entry]
    C1j --> C1k[PR Items Section]
    
    C1k --> C1l[Item Selection Interface]
    C1l --> C1m[Browse Item Master]
    C1m --> C1n[Select Item]
    C1n --> C1o{Item Selected?}
    C1o -->|No| C1p[Item Selection Required]
    C1o -->|Yes| C1q[Item Details Entry]
    
    C1p --> C1m
    C1q --> C1r[Quantity Entry]
    C1r --> C1s[UOM Verification]
    C1s --> C1t[Specification Entry]
    C1t --> C1u[Expected Rate Entry]
    C1u --> C1v[Required Date Entry]
    C1v --> C1w[Add Item to PR]
    C1w --> C1x{More Items?}
    C1x -->|Yes| C1l
    C1x -->|No| C1y[PR Validation]
    
    C1y --> C1z{Validation Passed?}
    C1z -->|No| C1aa[Show Validation Errors]
    C1z -->|Yes| C1bb[Save PR Master]
    
    C1aa --> C1c
    C1bb --> C1cc[Generate PR Number]
    C1cc --> C1dd[Set System Information]
    C1dd --> C1ee[Set Company ID]
    C1ee --> C1ff[Set Session Details]
    C1ff --> C1gg[Set Financial Year]
    C1gg --> C1hh[Set PR Status - Draft]
    C1hh --> C1ii[Execute Database Insert]
    C1ii --> C1jj{Insert Successful?}
    C1jj -->|No| C1kk[Database Error Message]
    C1jj -->|Yes| C1ll[Save PR Items]
    
    C1kk --> C1c
    C1ll --> C1mm[Loop Through PR Items]
    C1mm --> C1nn[Create PR Detail Record]
    C1nn --> C1oo{More Items?}
    C1oo -->|Yes| C1mm
    C1oo -->|No| C1pp[PR Creation Success]
    
    C1pp --> C1qq[Clear Form]
    C1qq --> C1rr[Refresh PR Grid]
    C1rr --> C1ss[Return to PR Dashboard]

    %% Edit PR
    C2 --> C2a[Select PR for Edit]
    C2a --> C2b[Check PR Status]
    C2b --> C2c{PR Editable?}
    C2c -->|No| C2d[PR Cannot be Edited]
    C2c -->|Yes| C2e[Navigate to Edit Form]
    
    C2d --> B7
    C2e --> C2f[Load PR Data]
    C2f --> C2g[Pre-populate Header Fields]
    C2g --> C2h[Load PR Items]
    C2h --> C2i[Display Items Grid]
    C2i --> C2j[Enable Field Modifications]
    C2j --> C2k[Update Header Information]
    C2k --> C2l[Modify PR Items]
    C2l --> C2m[Add New Items]
    C2m --> C2n[Remove Items]
    C2n --> C2o[Update Item Details]
    C2o --> C2p[Form Validation]
    C2p --> C2q{Validation Passed?}
    C2q -->|No| C2r[Show Validation Errors]
    C2q -->|Yes| C2s[Update PR Record]
    
    C2r --> C2k
    C2s --> C2t[Set Update Information]
    C2t --> C2u[Set Modified Date/Time]
    C2u --> C2v[Set Modified By User]
    C2v --> C2w[Execute Database Update]
    C2w --> C2x{Update Successful?}
    C2x -->|No| C2y[Update Error Message]
    C2x -->|Yes| C2z[Update Success Message]
    
    C2y --> C2k
    C2z --> C2aa[Refresh PR Grid]
    C2aa --> C2bb[Return to PR Dashboard]

    %% Delete PR
    C3 --> C3a[Select PR for Delete]
    C3a --> C3b[Check PR Status]
    C3b --> C3c{PR Deletable?}
    C3c -->|No| C3d[PR Cannot be Deleted]
    C3c -->|Yes| C3e[Navigate to Delete Confirmation]
    
    C3d --> B7
    C3e --> C3f[Load PR Details]
    C3f --> C3g[Display PR Information]
    C3g --> C3h[Show Delete Warning]
    C3h --> C3i[Confirm Deletion Dialog]
    C3i --> C3j{Confirm Delete?}
    C3j -->|No| C3k[Cancel Deletion]
    C3j -->|Yes| C3l[Check Dependencies]
    
    C3k --> B7
    C3l --> C3m[Check PO References]
    C3m --> C3n[Check Approval Status]
    C3n --> C3o{Has Dependencies?}
    C3o -->|Yes| C3p[Cannot Delete - Dependencies Exist]
    C3o -->|No| C3q[Execute Delete Operation]
    
    C3p --> B7
    C3q --> C3r[Delete PR Items]
    C3r --> C3s[Delete PR Master]
    C3s --> C3t{Delete Successful?}
    C3t -->|No| C3u[Delete Error Message]
    C3t -->|Yes| C3v[Delete Success Message]
    
    C3u --> B7
    C3v --> C3w[Refresh PR Grid]
    C3w --> C3x[Update PR Count]
    C3x --> B7

    %% View PR Details
    C4 --> C4a[Select PR for View]
    C4a --> C4b[Navigate to Details View]
    C4b --> C4c[Load Complete PR Data]
    C4c --> C4d[Display Header Information]
    C4d --> C4e[Display PR Items]
    C4e --> C4f[Display Approval History]
    C4f --> C4g[Display Status Information]
    C4g --> C4h[Display Related Documents]
    C4h --> C4i[Show Action Buttons]
    C4i --> C4j[Edit Option]
    C4j --> C4k[Delete Option]
    C4k --> C4l[Print Option]
    C4l --> C4m[Approve Option]

    %% Print PR
    C5 --> C5a[Select PR for Print]
    C5a --> C5b[Load Print Template]
    C5b --> C5c[Format PR Data]
    C5c --> C5d[Include Header Information]
    C5d --> C5e[Include PR Items]
    C5e --> C5f[Include Terms & Conditions]
    C5f --> C5g[Include Approval Details]
    C5g --> C5h[Generate Print Preview]
    C5h --> C5i[Print Document]
    C5i --> C5j[Save Print Log]

    %% PR Management
    D --> D1[PR Search & Filter]
    D --> D2[PR List Management]
    D --> D3[PR Status Management]

    %% PR Search & Filter
    D1 --> D1a[Search Interface]
    D1a --> D1b[PR Number Search]
    D1b --> D1c[Date Range Filter]
    D1c --> D1d[Department Filter]
    D1d --> D1e[Status Filter]
    D1e --> D1f[Priority Filter]
    D1f --> D1g[Requested By Filter]
    D1g --> D1h[Apply Search Filters]
    D1h --> D1i[Execute Search Query]
    D1i --> D1j[Display Filtered Results]
    D1j --> D1k[Update Result Count]
    D1k --> D1l[Enable Export Options]

    %% PR List Management
    D2 --> D2a[Grid Display Options]
    D2a --> D2b[Column Selection]
    D2b --> D2c[Sorting Options]
    D2c --> D2d[Pagination Settings]
    D2d --> D2e[Row Selection]
    D2e --> D2f[Bulk Action Selection]
    D2f --> D2g[Grid Refresh]
    D2g --> D2h[Export Grid Data]

    %% PR Status Management
    D3 --> D3a[Status Tracking]
    D3a --> D3b[Draft PRs]
    D3b --> D3c[Submitted PRs]
    D3c --> D3d[Approved PRs]
    D3d --> D3e[Rejected PRs]
    D3e --> D3f[Completed PRs]
    D3f --> D3g[Status Change Options]
    D3g --> D3h[Status Update Workflow]

    %% PR Workflow
    E --> E1[PR Approval Workflow]
    E --> E2[PR Authorization Workflow]
    E --> E3[PR Notification System]
    E --> E4[PR Integration]

    %% PR Approval Workflow
    E1 --> E1a[Submit for Approval]
    E1a --> E1b[Route to Approver]
    E1b --> E1c[Approval Review]
    E1c --> E1d[Approve/Reject Decision]
    E1d --> E1e{Approved?}
    E1e -->|Yes| E1f[Update Status - Approved]
    E1e -->|No| E1g[Update Status - Rejected]
    
    E1f --> E1h[Send Approval Notification]
    E1g --> E1i[Send Rejection Notification]
    E1h --> E1j[Route to Next Level]
    E1i --> E1k[Return to Requester]

    %% PR Authorization Workflow
    E2 --> E2a[Submit for Authorization]
    E2a --> E2b[Route to Authorizer]
    E2b --> E2c[Authorization Review]
    E2c --> E2d[Authorize/Reject Decision]
    E2d --> E2e{Authorized?}
    E2e -->|Yes| E2f[Update Status - Authorized]
    E2e -->|No| E2g[Update Status - Rejected]
    
    E2f --> E2h[Send Authorization Notification]
    E2g --> E2i[Send Rejection Notification]
    E2h --> E2j[Enable PO Creation]
    E2i --> E2k[Return to Previous Level]

    %% PR Notification System
    E3 --> E3a[Email Notifications]
    E3a --> E3b[SMS Alerts]
    E3b --> E3c[Dashboard Notifications]
    E3c --> E3d[Mobile Push Notifications]
    E3d --> E3e[Escalation Notifications]

    %% PR Integration
    E4 --> E4a[Purchase Order Integration]
    E4a --> E4b[Inventory Integration]
    E4b --> E4c[Budget Integration]
    E4c --> E4d[Project Integration]
    E4d --> E4e[Supplier Integration]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef operationClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef managementClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef workflowClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B10 initClass
    class C,C1,C2,C3,C4,C5 operationClass
    class D,D1,D2,D3 managementClass
    class E,E1,E2,E3,E4 workflowClass
    class C1o,C1x,C1z,C1jj,C1oo,C2c,C2q,C2x,C3c,C3j,C3o,C3t,E1e,E2e decisionClass
    class C1p,C1aa,C1kk,C2d,C2r,C2y,C3d,C3p,C3u errorClass
    class C1pp,C2z,C3v successClass
