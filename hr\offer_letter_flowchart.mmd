flowchart TD
    A[User Access Offer Letter Management] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Offer Letter Management Page]
    
    D --> E[Display Offer Letter Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_Offer_Master]
    G --> H[Display Grid with Pagination]
    H --> I[Show Employee Name, Position, Status]
    I --> J[Show Edit/Delete/Print Links]
    J --> F
    
    %% Create New Offer Letter
    F -->|Create New Offer| K[Load Offer Letter Creation Form]
    K --> L[Employee Information Section]
    L --> M[Employee Name Input]
    M --> N[Contact Details Input]
    N --> O[Address Information]
    O --> P[Position Details Section]
    P --> Q[Designation Selection]
    Q --> R[Department Assignment]
    R --> S[Grade Assignment]
    S --> T[Reporting Manager]
    T --> U[Salary Package Section]
    U --> V[Basic Salary Input]
    V --> W[Allowances Configuration]
    W --> X[Benefits Package]
    X --> Y[Terms & Conditions Section]
    Y --> Z[Joining Date]
    Z --> AA[Probation Period]
    AA --> BB[Notice Period]
    BB --> CC[Other Terms]
    CC --> DD{Validation Check}
    DD -->|Invalid| EE[Show Validation Errors]
    EE --> K
    DD -->|Valid| FF[Generate Offer Letter]
    FF --> GG[Insert into tblHR_Offer_Master]
    GG --> HH[Generate Offer Letter Document]
    HH --> II[Set Status to Draft]
    II --> JJ[Save Offer Letter]
    JJ --> KK[Show Success Message]
    KK --> F
    
    %% Edit Offer Letter
    F -->|Edit Offer| LL[Load Offer Letter for Editing]
    LL --> MM[Check Offer Status]
    MM --> NN{Status Check}
    NN -->|Sent/Accepted| OO[Show 'Cannot Edit - Already Sent' Error]
    OO --> F
    NN -->|Draft| PP[Enable Editing]
    PP --> QQ[Modify Offer Details]
    QQ --> RR[Update Offer Letter]
    RR --> SS[Regenerate Document]
    SS --> TT[Save Changes]
    TT --> F
    
    %% Send Offer Letter
    F -->|Send Offer| UU[Load Send Offer Interface]
    UU --> VV[Select Offer Letter]
    VV --> WW[Review Offer Details]
    WW --> XX[Verify Email Address]
    XX --> YY[Generate Final Document]
    YY --> ZZ[Send Email with Attachment]
    ZZ --> AAA[Update Status to Sent]
    AAA --> BBB[Log Send Activity]
    BBB --> CCC[Send Confirmation]
    CCC --> F
    
    %% Track Offer Status
    F -->|Track Status| DDD[Load Offer Tracking]
    DDD --> EEE[Display Offer Status]
    EEE --> FFF{Status Actions}
    FFF -->|Mark Accepted| GGG[Mark Offer as Accepted]
    FFF -->|Mark Rejected| HHH[Mark Offer as Rejected]
    FFF -->|Mark Withdrawn| III[Mark Offer as Withdrawn]
    FFF -->|Follow Up| JJJ[Send Follow-up Communication]
    
    GGG --> KKK[Update Status to Accepted]
    HHH --> LLL[Update Status to Rejected]
    III --> MMM[Update Status to Withdrawn]
    JJJ --> NNN[Send Follow-up Email/SMS]
    
    KKK --> OOO[Initiate Onboarding Process]
    LLL --> PPP[Log Rejection Reason]
    MMM --> QQQ[Log Withdrawal Reason]
    NNN --> RRR[Log Follow-up Activity]
    
    OOO --> F
    PPP --> F
    QQQ --> F
    RRR --> F
    
    %% Offer Letter Templates
    F -->|Manage Templates| SSS[Load Template Management]
    SSS --> TTT{Template Action}
    TTT -->|Create Template| UUU[Create New Template]
    TTT -->|Edit Template| VVV[Edit Existing Template]
    TTT -->|Delete Template| WWW[Delete Template]
    
    UUU --> XXX[Design Template Layout]
    VVV --> YYY[Modify Template Content]
    WWW --> ZZZ[Confirm Template Deletion]
    
    XXX --> AAAA[Save New Template]
    YYY --> BBBB[Update Template]
    ZZZ --> CCCC[Remove Template]
    
    AAAA --> DDDD[Template Management Complete]
    BBBB --> DDDD
    CCCC --> DDDD
    DDDD --> F
    
    %% Bulk Operations
    F -->|Bulk Operations| EEEE[Load Bulk Operations]
    EEEE --> FFFF{Bulk Action}
    FFFF -->|Bulk Create| GGGG[Bulk Offer Creation]
    FFFF -->|Bulk Send| HHHH[Bulk Offer Sending]
    FFFF -->|Bulk Update| IIII[Bulk Status Update]
    
    GGGG --> JJJJ[Upload Candidate Data]
    HHHH --> KKKK[Select Multiple Offers]
    IIII --> LLLL[Select Offers for Update]
    
    JJJJ --> MMMM[Process Bulk Creation]
    KKKK --> NNNN[Process Bulk Sending]
    LLLL --> OOOO[Process Bulk Updates]
    
    MMMM --> PPPP[Generate Bulk Report]
    NNNN --> PPPP
    OOOO --> PPPP
    PPPP --> F
    
    %% Offer Letter Reports
    F -->|Reports| QQQQ[Offer Letter Reporting]
    QQQQ --> RRRR{Report Type}
    RRRR -->|Status Report| SSSS[Offer Status Report]
    RRRR -->|Acceptance Rate| TTTT[Acceptance Rate Analysis]
    RRRR -->|Department Report| UUUU[Department-wise Offers]
    RRRR -->|Timeline Report| VVVV[Offer Timeline Analysis]
    
    SSSS --> WWWW[Generate Status Report]
    TTTT --> XXXX[Generate Acceptance Analysis]
    UUUU --> YYYY[Generate Department Report]
    VVVV --> ZZZZ[Generate Timeline Report]
    
    WWWW --> AAAAA[Export Offer Reports]
    XXXX --> AAAAA
    YYYY --> AAAAA
    ZZZZ --> AAAAA
    AAAAA --> F
    
    %% Integration with Employee Management
    F -->|Convert to Employee| BBBBB[Convert Accepted Offer to Employee]
    BBBBB --> CCCCC[Verify Offer Acceptance]
    CCCCC --> DDDDD[Create Employee Record]
    DDDDD --> EEEEE[Transfer Offer Data]
    EEEEE --> FFFFF[Generate Employee ID]
    FFFFF --> GGGGG[Setup Employee Profile]
    GGGGG --> HHHHH[Initiate Onboarding]
    HHHHH --> IIIII[Update Offer Status to Converted]
    IIIII --> F
    
    %% Business Rules
    JJJJJ[Business Rules] --> KKKKK[Employee name required]
    KKKKK --> LLLLL[Valid email address required]
    LLLLL --> MMMMM[Salary package validation]
    MMMMM --> NNNNN[Joining date future validation]
    NNNNN --> OOOOO[Designation-grade compatibility]
    OOOOO --> PPPPP[Approval workflow for high packages]
    PPPPP --> DD
    
    %% Django Implementation
    QQQQQ[Django Implementation] --> RRRRR[OfferLetter Model]
    RRRRR --> SSSSS[OfferLetterTemplate Model]
    SSSSS --> TTTTT[OfferLetterForm Validation]
    TTTTT --> UUUUU[Offer Management Views]
    UUUUU --> VVVVV[Template Management Views]
    VVVVV --> WWWWW[Document Generation Views]
    WWWWW --> XXXXX[SAP Fiori UI Templates]
    XXXXX --> YYYYY[HTMX Dynamic Updates]
    YYYYY --> F
    
    %% Integration Points
    ZZZZZ[Integration Points] --> AAAAAA[Employee Management]
    AAAAAA --> BBBBBB[Email System]
    BBBBBB --> CCCCCC[Document Management]
    CCCCCC --> DDDDDD[Approval Workflow]
    DDDDDD --> EEEEEE[Onboarding Process]
    EEEEEE --> FFFFFF[Payroll Setup]
    FFFFFF --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style FF fill:#e8f5e8
    style RR fill:#e8f5e8
    style ZZ fill:#e8f5e8
    style KKK fill:#e8f5e8
    style LLL fill:#ffebee
    style III fill:#ffebee
    style AAAA fill:#e8f5e8
    style BBBB fill:#e8f5e8
    style CCCC fill:#ffebee
    style MMMM fill:#e8f5e8
    style NNNN fill:#e8f5e8
    style OOOO fill:#e8f5e8
    style EE fill:#fff3e0
    style OO fill:#fff3e0
    style WWWW fill:#fff3e0
    style XXXX fill:#fff3e0
    style YYYY fill:#fff3e0
    style ZZZZ fill:#fff3e0
    style DDDDD fill:#e8f5e8
    style QQQQQ fill:#f1f8e9
    style JJJJJ fill:#e3f2fd
    style ZZZZZ fill:#e0f2f1
