flowchart TD
    A[User Access SwapCard No Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load SwapCard No Master Page]
    
    D --> E[Display SwapCard Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_SwapCard]
    G --> H[Display Grid with Pagination]
    H --> I[Show Card Number, Employee, Status]
    I --> J[Show Edit/Delete/Assign Links]
    J --> F
    
    %% Add New SwapCard
    F -->|Add New Card| K[Show Add SwapCard Form]
    K --> L[User Enters Card Details]
    L --> M[Card Number Input]
    M --> N[Card Type Selection]
    N --> O[Card Status Setting]
    O --> P{Validation Check}
    P -->|Empty Fields| Q[Show Required Field Error]
    Q --> L
    P -->|Valid Data| R[Check Card Number Uniqueness]
    R --> S{Card Number Exists?}
    S -->|Yes| T[Show 'Card Number Already Exists' Alert]
    T --> L
    S -->|No| U[Insert into tblHR_SwapCard]
    U --> V{Insert Success?}
    V -->|Success| W[Show 'Card Added' Message]
    V -->|Error| X[Show Error Message]
    W --> Y[Refresh Grid Display]
    X --> Y
    Y --> F
    
    %% Assign Card to Employee
    F -->|Assign Card| Z[Show Card Assignment Form]
    Z --> AA[Select Available Card]
    AA --> BB[Search Employee]
    BB --> CC[Employee Selection]
    CC --> DD[Verify Employee Eligibility]
    DD --> EE{Employee Eligible?}
    EE -->|No| FF[Show Eligibility Error]
    FF --> Z
    EE -->|Yes| GG[Check Existing Assignment]
    GG --> HH{Employee Has Card?}
    HH -->|Yes| II[Show 'Employee Already Has Card' Alert]
    II --> Z
    HH -->|No| JJ[Assign Card to Employee]
    JJ --> KK[Update Card Status to Assigned]
    KK --> LL[Set Assignment Date]
    LL --> MM[Record Assignment Details]
    MM --> NN[Generate Assignment Report]
    NN --> OO[Send Assignment Notification]
    OO --> PP[Card Assignment Complete]
    PP --> F
    
    %% Unassign/Return Card
    F -->|Return Card| QQ[Show Card Return Form]
    QQ --> RR[Search Assigned Cards]
    RR --> SS[Select Card to Return]
    SS --> TT[Verify Return Authorization]
    TT --> UU{Authorized to Return?}
    UU -->|No| VV[Show Authorization Error]
    VV --> QQ
    UU -->|Yes| WW[Process Card Return]
    WW --> XX[Update Card Status to Available]
    XX --> YY[Clear Employee Assignment]
    YY --> ZZ[Set Return Date]
    ZZ --> AAA[Record Return Reason]
    AAA --> BBB[Generate Return Report]
    BBB --> CCC[Send Return Notification]
    CCC --> DDD[Card Return Complete]
    DDD --> F
    
    %% Card Status Management
    F -->|Manage Status| EEE[Load Card Status Management]
    EEE --> FFF{Status Action}
    FFF -->|Activate| GGG[Activate Card]
    FFF -->|Deactivate| HHH[Deactivate Card]
    FFF -->|Block| III[Block Card]
    FFF -->|Replace| JJJ[Replace Card]
    
    GGG --> KKK[Update Status to Active]
    HHH --> LLL[Update Status to Inactive]
    III --> MMM[Update Status to Blocked]
    JJJ --> NNN[Issue New Card]
    
    KKK --> OOO[Log Status Change]
    LLL --> OOO
    MMM --> OOO
    NNN --> OOO
    OOO --> PPP[Send Status Notification]
    PPP --> F
    
    %% Card Tracking and Reports
    F -->|Track Cards| QQQ[Card Tracking Interface]
    QQQ --> RRR{Tracking Type}
    RRR -->|By Employee| SSS[Employee-wise Card Report]
    RRR -->|By Status| TTT[Status-wise Card Report]
    RRR -->|By Date| UUU[Date-wise Assignment Report]
    RRR -->|Lost Cards| VVV[Lost Card Report]
    
    SSS --> WWW[Generate Employee Report]
    TTT --> XXX[Generate Status Report]
    UUU --> YYY[Generate Date Report]
    VVV --> ZZZ[Generate Lost Card Report]
    
    WWW --> AAAA[Export Report]
    XXX --> AAAA
    YYY --> AAAA
    ZZZ --> AAAA
    AAAA --> F
    
    %% Lost/Damaged Card Management
    F -->|Report Lost/Damaged| BBBB[Lost Card Reporting]
    BBBB --> CCCC[Employee Reports Lost Card]
    CCCC --> DDDD[Verify Employee Identity]
    DDDD --> EEEE[Record Loss Details]
    EEEE --> FFFF[Block Lost Card]
    FFFF --> GGGG[Issue Replacement Card]
    GGGG --> HHHH[Update Card Records]
    HHHH --> IIII[Generate Loss Report]
    IIII --> JJJJ[Send Replacement Notification]
    JJJJ --> F
    
    %% Business Rules
    KKKK[Business Rules] --> LLLL[Card Number must be unique]
    LLLL --> MMMM[One card per employee]
    MMMM --> NNNN[Card status validation]
    NNNN --> OOOO[Assignment authorization required]
    OOOO --> PPPP[Return process mandatory]
    PPPP --> QQQQ[Lost card blocking required]
    QQQQ --> P
    
    %% Django Implementation
    RRRR[Django Implementation] --> SSSS[SwapCard Model]
    SSSS --> TTTT[CardAssignment Model]
    TTTT --> UUUU[SwapCardForm Validation]
    UUUU --> VVVV[Card Management Views]
    VVVV --> WWWW[Assignment Workflow Views]
    WWWW --> XXXX[Tracking & Reporting Views]
    XXXX --> YYYY[SAP Fiori UI Templates]
    YYYY --> ZZZZ[HTMX Dynamic Updates]
    ZZZZ --> F
    
    %% Integration Points
    AAAAA[Integration Points] --> BBBBB[Employee Management]
    BBBBB --> CCCCC[Access Control System]
    CCCCC --> DDDDD[Security Management]
    DDDDD --> EEEEE[Attendance System]
    EEEEE --> FFFFF[Asset Management]
    FFFFF --> GGGGG[Facility Management]
    GGGGG --> D
    
    %% Security Features
    HHHHH[Security Features] --> IIIII[Card Authentication]
    IIIII --> JJJJJ[Access Level Management]
    JJJJJ --> KKKKK[Time-based Access]
    KKKKK --> LLLLL[Location-based Access]
    LLLLL --> MMMMM[Emergency Override]
    MMMMM --> NNNNN[Audit Trail Logging]
    NNNNN --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style U fill:#e8f5e8
    style JJ fill:#e8f5e8
    style WW fill:#e8f5e8
    style GGG fill:#e8f5e8
    style HHH fill:#e8f5e8
    style III fill:#e8f5e8
    style JJJ fill:#e8f5e8
    style T fill:#fff3e0
    style Q fill:#fff3e0
    style FF fill:#fff3e0
    style II fill:#fff3e0
    style VV fill:#fff3e0
    style FFFF fill:#ffebee
    style RRRR fill:#f1f8e9
    style KKKK fill:#e3f2fd
    style AAAAA fill:#e0f2f1
    style HHHHH fill:#f3e5f5
