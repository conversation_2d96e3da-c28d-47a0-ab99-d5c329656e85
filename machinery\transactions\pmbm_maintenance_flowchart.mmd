graph TD
    A[PMBM - Preventive/Breakdown Maintenance] --> B[Machine Selection]
    A --> C[Maintenance Management]
    A --> D[Maintenance Tracking]

    %% Machine Selection for PMBM
    B --> B1[Load Registered Machines]
    B1 --> B2[Clear Temp Tables]
    B2 --> B3[Apply Company Filter]
    B3 --> B4[Calculate PM Status]
    B4 --> B5[Search & Filter Options]
    
    B5 --> B6[Category Filter]
    B5 --> B7[Subcategory Filter]
    B5 --> B8[Item Code Search]
    B5 --> B9[Description Search]
    
    B6 --> B10[Filter by Category]
    B7 --> B11[Filter by Subcategory]
    B8 --> B12[Filter by Item Code]
    B9 --> B13[Filter by Description]
    
    B10 --> B14[Apply Filters]
    B11 --> B14
    B12 --> B14
    B13 --> B14
    
    B14 --> B15[Calculate PM Status for Each Machine]
    B15 --> B16[Get Last Maintenance Date]
    B16 --> B17[Calculate Days Since Last PM]
    B17 --> B18[Check PM Days Configuration]
    B18 --> B19[Calculate Remaining Days]
    B19 --> B20[Determine PM Status]
    
    B20 --> B21{PM Status}
    B21 -->|Overdue| B22[Mark as Overdue]
    B21 -->|Due Soon| B23[Mark as Due Soon]
    B21 -->|Current| B24[Mark as Current]
    
    B22 --> B25[Display with Red Alert]
    B23 --> B26[Display with Yellow Alert]
    B24 --> B27[Display with Green Status]
    
    B25 --> B28[Machine List Display]
    B26 --> B28
    B27 --> B28
    
    B28 --> B29[Select Machine for PMBM]
    B29 --> B30{Machine Selected?}
    B30 -->|Yes| B31[Navigate to PMBM Details]
    B30 -->|No| B32[Stay on Selection Page]
    
    B31 --> C1[PMBM Details Form]

    %% PMBM Details Form Flow
    C1 --> C2[Load Machine Information]
    C2 --> C3[Load Item Details]
    C3 --> C4[Display Machine Specifications]
    C4 --> C5[Show Maintenance History]
    C5 --> C6[Show Machine Spare Parts]
    C6 --> C7[PMBM Form Fields]
    
    C7 --> C8[Maintenance Type Selection]
    C8 --> C9{Maintenance Type}
    C9 -->|PM| C10[Preventive Maintenance]
    C9 -->|BM| C11[Breakdown Maintenance]
    
    C10 --> C12[Schedule PM Date]
    C11 --> C13[Record Breakdown Date]
    
    C12 --> C14[From Date Selection]
    C13 --> C14
    C14 --> C15[To Date Selection]
    C15 --> C16[From Time Entry]
    C16 --> C17[To Time Entry]
    C17 --> C18[Engineer Assignment]
    
    C18 --> C19[Name of Engineer]
    C19 --> C20[Agency Details]
    C20 --> C21[Name of Agency]
    C21 --> C22[Spare Parts Used]
    
    C22 --> C23[Select Spare Parts]
    C23 --> C24[Enter Quantities Used]
    C24 --> C25[Add to Maintenance Details]
    C25 --> C26{More Spare Parts?}
    C26 -->|Yes| C23
    C26 -->|No| C27[Calculate Next PM Due]
    
    C27 --> C28{Is Preventive Maintenance?}
    C28 -->|Yes| C29[Calculate Next PM Date]
    C28 -->|No| C30[Skip PM Calculation]
    
    C29 --> C31[Add PM Days to From Date]
    C31 --> C32[Set Next PM Due Date]
    C32 --> C33[Remarks Entry]
    C30 --> C33
    
    C33 --> C34[Enter Maintenance Remarks]
    C34 --> C35[Form Validation]
    C35 --> C36{Validation Passed?}
    C36 -->|No| C37[Show Validation Errors]
    C36 -->|Yes| C38[Save PMBM Record]
    
    C37 --> C7
    
    C38 --> C39[Generate System Date/Time]
    C39 --> C40[Set User & Company Info]
    C40 --> C41[Set Financial Year]
    C41 --> C42[Set Machine ID]
    C42 --> C43[Save to PMBM Master Table]
    
    C43 --> C44[Save Spare Parts Details]
    C44 --> C45[Loop Through Used Spares]
    C45 --> C46[Create PMBM Detail Record]
    C46 --> C47{More Spare Details?}
    C47 -->|Yes| C45
    C47 -->|No| C48[Update Machine Status]
    
    C48 --> C49[Update Last Maintenance Date]
    C49 --> C50[Update Next PM Due Date]
    C50 --> C51[Generate PM Schedule]
    C51 --> C52[Success Message]
    C52 --> C53[Redirect to PMBM List]

    %% Maintenance Management Flow
    C --> D1[Maintenance Records List]
    D1 --> D2[Load PMBM Records]
    D2 --> D3[Apply Company Filter]
    D3 --> D4[Calculate Due Dates]
    D4 --> D5[Enhance with Machine Info]
    D5 --> D6[Display Maintenance List]
    
    D6 --> D7[Maintenance Actions]
    D7 --> D8[View Details]
    D7 --> D9[Edit Maintenance]
    D7 --> D10[Delete Maintenance]
    D7 --> D11[Print Maintenance]
    
    %% View Maintenance Details
    D8 --> D12[Load Maintenance Record]
    D12 --> D13[Display PMBM Details]
    D13 --> D14[Show Machine Information]
    D14 --> D15[Show Engineer Details]
    D15 --> D16[Show Agency Information]
    D16 --> D17[Show Spare Parts Used]
    D17 --> D18[Show Time Duration]
    D18 --> D19[Show Next PM Due Date]
    D19 --> D20[Show Remarks]
    
    %% Edit Maintenance
    D9 --> D21[Load Maintenance for Edit]
    D21 --> D22[Pre-populate Form]
    D22 --> D23[Allow Modifications]
    D23 --> D24[Update Spare Parts]
    D24 --> D25[Recalculate Next PM Date]
    D25 --> D26[Save Changes]
    D26 --> D27[Update System Date/Time]
    D27 --> D28[Success Message]
    
    %% Delete Maintenance
    D10 --> D29[Confirm Deletion]
    D29 --> D30{Confirm Delete?}
    D30 -->|No| D6
    D30 -->|Yes| D31[Delete PMBM Record]
    D31 --> D32[Delete Spare Parts Details]
    D32 --> D33[Update Machine Status]
    D33 --> D34[Success Message]
    D34 --> D6

    %% Maintenance Tracking Section
    D --> E1[Maintenance Dashboard]
    E1 --> E2[Overdue Machines]
    E1 --> E3[Due Soon Machines]
    E1 --> E4[Maintenance History]
    E1 --> E5[Alert Generation]
    
    %% Overdue Machines
    E2 --> E6[Calculate Overdue Status]
    E6 --> E7[Get Machines with Expired PM]
    E7 --> E8[Calculate Days Overdue]
    E8 --> E9[Priority Assignment]
    E9 --> E10[Generate Overdue Alerts]
    E10 --> E11[Display Overdue List]
    
    %% Due Soon Machines
    E3 --> E12[Calculate Due Soon Status]
    E12 --> E13[Get Machines Due in 7 Days]
    E13 --> E14[Calculate Days Remaining]
    E14 --> E15[Generate Due Soon Alerts]
    E15 --> E16[Display Due Soon List]
    
    %% Maintenance History
    E4 --> E17[Load Historical Records]
    E17 --> E18[Group by Machine]
    E18 --> E19[Sort by Date]
    E19 --> E20[Calculate Trends]
    E20 --> E21[Generate History Reports]
    E21 --> E22[Display History View]
    
    %% Alert Generation
    E5 --> E23[Notification System]
    E23 --> E24[Email Alerts]
    E23 --> E25[Dashboard Notifications]
    E23 --> E26[SMS Alerts]
    E24 --> E27[Send to Maintenance Team]
    E25 --> E28[Display on Dashboard]
    E26 --> E29[Send to Supervisors]
    
    E27 --> E30[Alert Tracking]
    E28 --> E30
    E29 --> E30
    E30 --> E31[Response Monitoring]
    E31 --> E32[Follow-up Actions]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef processClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef alertClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef warningClass fill:#fff8e1,stroke:#f9a825,stroke-width:2px

    class A startClass
    class B21,B30,C9,C26,C28,C36,C47,D30 decisionClass
    class B22,E10,E15 alertClass
    class B23,E12,E13 warningClass
    class C52,D28,D34 successClass
    class C38,C43,C44,C48,D12,D21,D31 dataClass
