flowchart TD
    A[User Access Intercom Extension Master] --> B{Authentication Check}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load Intercom Extension Master Page]
    
    D --> E[Display Intercom Extension Grid]
    E --> F{User Action}
    
    %% View/Display Operations
    F -->|View Records| G[Query tblHR_IntercomExt]
    G --> H[Display Grid with Pagination]
    H --> I[Show Extension No & Department]
    I --> J[Show Edit/Delete/Assign Links]
    J --> F
    
    %% Add New Intercom Extension
    F -->|Add New Extension| K[Show Add Extension Form]
    K --> L[User Enters Extension Details]
    L --> M[Extension Number Input]
    M --> N[Department Selection]
    N --> O{Validation Check}
    O -->|Empty Fields| P[Show Required Field Error]
    P --> L
    O -->|Invalid Extension| Q[Show Extension Format Error]
    Q --> L
    O -->|Valid Data| R[Check Extension Number Uniqueness]
    R --> S{Extension Number Exists?}
    S -->|Yes| T[Show 'Extension Already Exists' Alert]
    T --> L
    S -->|No| U[Insert into tblHR_IntercomExt]
    U --> V{Insert Success?}
    V -->|Success| W[Show 'Extension Added' Message]
    V -->|Error| X[Show Error Message]
    W --> Y[Refresh Grid Display]
    X --> Y
    Y --> F
    
    %% Edit Intercom Extension
    F -->|Edit Record| Z[Enable Edit Mode for Row]
    Z --> AA[User Modifies Extension/Department]
    AA --> BB{Update Validation}
    BB -->|Invalid| CC[Show Validation Error]
    CC --> AA
    BB -->|Valid| DD[Confirm Update Action]
    DD --> EE{User Confirms?}
    EE -->|No| FF[Cancel Edit Mode]
    FF --> F
    EE -->|Yes| GG[Update tblHR_IntercomExt Record]
    GG --> HH{Update Success?}
    HH -->|Success| II[Show 'Record Updated' Message]
    HH -->|Error| JJ[Show Update Error]
    II --> KK[Refresh Grid Display]
    JJ --> KK
    KK --> F
    
    %% Delete Intercom Extension
    F -->|Delete Record| LL[Show Delete Confirmation]
    LL --> MM{User Confirms Delete?}
    MM -->|No| F
    MM -->|Yes| NN[Check Extension Usage]
    NN --> OO{Extension in Use?}
    OO -->|Yes| PP[Show 'Cannot Delete - In Use' Error]
    PP --> F
    OO -->|No| QQ[Delete from tblHR_IntercomExt]
    QQ --> RR{Delete Success?}
    RR -->|Success| SS[Show 'Record Deleted' Message]
    RR -->|Error| TT[Show Delete Error]
    SS --> UU[Refresh Grid Display]
    TT --> UU
    UU --> F
    
    %% Extension Assignment Management
    F -->|Assign Extension| VV[Load Extension Assignment]
    VV --> WW[Select Available Extension]
    WW --> XX[Search Employee]
    XX --> YY[Employee Selection]
    YY --> ZZ[Verify Assignment Eligibility]
    ZZ --> AAA{Employee Eligible?}
    AAA -->|No| BBB[Show Eligibility Error]
    BBB --> VV
    AAA -->|Yes| CCC[Check Department Match]
    CCC --> DDD{Department Matches?}
    DDD -->|No| EEE[Show Department Mismatch Warning]
    EEE --> FFF{Continue Assignment?}
    FFF -->|No| VV
    FFF -->|Yes| GGG[Proceed with Assignment]
    DDD -->|Yes| GGG
    GGG --> HHH[Assign Extension to Employee]
    HHH --> III[Set Assignment Date]
    III --> JJJ[Record Assignment Details]
    JJJ --> KKK[Update Extension Status]
    KKK --> LLL[Generate Assignment Report]
    LLL --> MMM[Send Assignment Notification]
    MMM --> NNN[Extension Assignment Complete]
    NNN --> F
    
    %% Extension Directory Management
    F -->|Manage Directory| OOO[Load Extension Directory]
    OOO --> PPP[Display Complete Directory]
    PPP --> QQQ[Show Department-wise Extensions]
    QQQ --> RRR[Show Employee Assignments]
    RRR --> SSS{Directory Action}
    
    SSS -->|Update Directory| TTT[Update Extension Directory]
    TTT --> UUU[Refresh Employee Information]
    UUU --> VVV[Update Department Changes]
    VVV --> WWW[Generate Updated Directory]
    WWW --> XXX[Publish Directory]
    XXX --> F
    
    SSS -->|Print Directory| YYY[Generate Printable Directory]
    YYY --> ZZZ[Format Directory Report]
    ZZZ --> AAAA[Include Department Sections]
    AAAA --> BBBB[Include Employee Details]
    BBBB --> CCCC[Generate PDF Directory]
    CCCC --> DDDD[Print Directory Document]
    DDDD --> F
    
    %% Extension Monitoring
    F -->|Monitor Extensions| EEEE[Extension Monitoring Dashboard]
    EEEE --> FFFF{Monitoring Type}
    FFFF -->|Usage Statistics| GGGG[Extension Usage Reports]
    FFFF -->|Assignment Status| HHHH[Assignment Status Reports]
    FFFF -->|Department Analysis| IIII[Department-wise Analysis]
    FFFF -->|Maintenance Tracking| JJJJ[Maintenance & Issues Tracking]
    
    GGGG --> KKKK[Generate Usage Statistics]
    HHHH --> LLLL[Generate Assignment Report]
    IIII --> MMMM[Generate Department Analysis]
    JJJJ --> NNNN[Generate Maintenance Report]
    
    KKKK --> OOOO[Export Monitoring Reports]
    LLLL --> OOOO
    MMMM --> OOOO
    NNNN --> OOOO
    OOOO --> F
    
    %% Extension Maintenance
    F -->|Maintenance| PPPP[Extension Maintenance Management]
    PPPP --> QQQQ{Maintenance Action}
    QQQQ -->|Schedule Maintenance| RRRR[Schedule Extension Maintenance]
    QQQQ -->|Report Issue| SSSS[Report Extension Issue]
    QQQQ -->|Update Status| TTTT[Update Extension Status]
    QQQQ -->|Replacement| UUUU[Extension Replacement]
    
    RRRR --> VVVV[Create Maintenance Schedule]
    SSSS --> WWWW[Log Issue Details]
    TTTT --> XXXX[Update Operational Status]
    UUUU --> YYYY[Process Extension Replacement]
    
    VVVV --> ZZZZ[Send Maintenance Notifications]
    WWWW --> ZZZZ
    XXXX --> ZZZZ
    YYYY --> ZZZZ
    ZZZZ --> F
    
    %% Business Rules
    AAAAA[Business Rules] --> BBBBB[Extension number must be unique]
    BBBBB --> CCCCC[Extension format validation]
    CCCCC --> DDDDD[Department assignment required]
    DDDDD --> EEEEE[One extension per employee]
    EEEEE --> FFFFF[Department-extension mapping]
    FFFFF --> GGGGG[Extension availability tracking]
    GGGGG --> O
    
    %% Django Implementation
    HHHHH[Django Implementation] --> IIIII[IntercomExtension Model]
    IIIII --> JJJJJ[ExtensionAssignment Model]
    JJJJJ --> KKKKK[ExtensionDirectory Model]
    KKKKK --> LLLLL[IntercomExtensionForm Validation]
    LLLLL --> MMMMM[Extension Management Views]
    MMMMM --> NNNNN[Directory Management Views]
    NNNNN --> OOOOO[Monitoring & Reporting Views]
    OOOOO --> PPPPP[SAP Fiori UI Templates]
    PPPPP --> QQQQQ[HTMX Dynamic Updates]
    QQQQQ --> F
    
    %% Integration Points
    RRRRR[Integration Points] --> SSSSS[Employee Management]
    SSSSS --> TTTTT[Department Management]
    TTTTT --> UUUUU[Communication Systems]
    UUUUU --> VVVVV[Facility Management]
    VVVVV --> WWWWW[Asset Management]
    WWWWW --> XXXXX[Maintenance Management]
    XXXXX --> D
    
    %% Communication Features
    YYYYY[Communication Features] --> ZZZZZ[Internal Directory]
    ZZZZZ --> AAAAAA[Quick Dial Features]
    AAAAAA --> BBBBBB[Conference Call Setup]
    BBBBBB --> CCCCCC[Call Forwarding Rules]
    CCCCCC --> DDDDDD[Emergency Contact System]
    DDDDDD --> EEEEEE[Voicemail Integration]
    EEEEEE --> D
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style U fill:#e8f5e8
    style GG fill:#e8f5e8
    style QQ fill:#ffebee
    style HHH fill:#e8f5e8
    style TTT fill:#e8f5e8
    style YYY fill:#e8f5e8
    style T fill:#fff3e0
    style P fill:#fff3e0
    style Q fill:#fff3e0
    style BBB fill:#fff3e0
    style EEE fill:#fff3e0
    style RRRR fill:#e8f5e8
    style SSSS fill:#e8f5e8
    style TTTT fill:#e8f5e8
    style UUUU fill:#e8f5e8
    style HHHHH fill:#f1f8e9
    style AAAAA fill:#e3f2fd
    style RRRRR fill:#e0f2f1
    style YYYYY fill:#f3e5f5
