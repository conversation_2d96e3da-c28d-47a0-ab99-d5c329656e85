graph TD
    A[Machinery Module] --> B[Masters]
    A --> C[Transactions]
    A --> D[Reports]
    A --> E[Dashboard]

    %% Masters Section
    B --> B1[Machine Registration]
    B --> B2[Machine Management]
    B --> B3[Machine Search & Filter]

    %% Machine Registration Flow
    B1 --> B1a[Select Item for Machine]
    B1a --> B1b{Item Available?}
    B1b -->|Yes| B1c[Machine Details Form]
    B1b -->|No| B1d[Return to Item Selection]
    
    B1c --> B1e[Basic Machine Info]
    B1e --> B1f[Make, Model, Capacity]
    B1f --> B1g[Purchase Details]
    B1g --> B1h[Supplier Information]
    B1h --> B1i[Cost & Warranty]
    B1i --> B1j[Insurance Details]
    B1j --> B1k[Location & Incharge]
    B1k --> B1l[PM Days Configuration]
    B1l --> B1m[Spare Parts Selection]
    B1m --> B1n[Process Assignment]
    B1n --> B1o[File Attachment]
    B1o --> B1p{Validation Check}
    B1p -->|Valid| B1q[Save Machine Master]
    B1p -->|Invalid| B1r[Show Validation Errors]
    B1r --> B1c
    B1q --> B1s[Update Machine Tables]
    B1s --> B1t[Save Spare Parts]
    B1t --> B1u[Save Processes]
    B1u --> B1v[Clear Temp Tables]
    B1v --> B1w[Success Message]

    %% Machine Management Flow
    B2 --> B2a[Machine List View]
    B2a --> B2b[Edit Machine]
    B2a --> B2c[View Machine Details]
    B2a --> B2d[Delete Machine]
    B2a --> B2e[Print Machine Info]
    
    B2b --> B2f[Update Machine Form]
    B2f --> B2g[Modify Details]
    B2g --> B2h[Update Spare Parts]
    B2h --> B2i[Update Processes]
    B2i --> B2j[Save Changes]
    
    B2c --> B2k[Show Machine Details]
    B2k --> B2l[Display Specifications]
    B2l --> B2m[Show Spare Parts]
    B2m --> B2n[Show Processes]
    B2n --> B2o[Maintenance History]
    B2o --> B2p[Insurance Status]

    %% Machine Search & Filter
    B3 --> B3a[Search Criteria]
    B3a --> B3b[Category Filter]
    B3a --> B3c[Subcategory Filter]
    B3a --> B3d[Item Code Search]
    B3a --> B3e[Description Search]
    B3a --> B3f[Location Search]
    B3b --> B3g[Apply Filters]
    B3c --> B3g
    B3d --> B3g
    B3e --> B3g
    B3f --> B3g
    B3g --> B3h[Display Results]
    B3h --> B3i[Pagination]

    %% Transactions Section
    C --> C1[PMBM Management]
    C --> C2[Job Scheduling]
    C --> C3[Schedule Output]
    C --> C4[Maintenance Tracking]

    %% PMBM (Preventive/Breakdown Maintenance) Flow
    C1 --> C1a[PMBM Dashboard]
    C1a --> C1b[Machine Selection]
    C1b --> C1c[PM Status Check]
    C1c --> C1d{Maintenance Due?}
    C1d -->|Yes| C1e[Create PMBM Record]
    C1d -->|No| C1f[Show Status Info]
    
    C1e --> C1g[PMBM Details Form]
    C1g --> C1h[Maintenance Type]
    C1h --> C1i{PM or BM?}
    C1i -->|PM| C1j[Preventive Maintenance]
    C1i -->|BM| C1k[Breakdown Maintenance]
    
    C1j --> C1l[Schedule PM Date]
    C1k --> C1m[Record Breakdown]
    C1l --> C1n[Assign Engineer]
    C1m --> C1n
    C1n --> C1o[Agency Details]
    C1o --> C1p[Spare Parts Used]
    C1p --> C1q[Time Duration]
    C1q --> C1r[Next PM Due Date]
    C1r --> C1s[Remarks]
    C1s --> C1t[Save PMBM Record]
    C1t --> C1u[Update Machine Status]
    C1u --> C1v[Generate PM Schedule]

    %% Job Scheduling Flow
    C2 --> C2a[Work Order Selection]
    C2a --> C2b[Search Work Orders]
    C2b --> C2c{Work Order Found?}
    C2c -->|Yes| C2d[Work Order Details]
    C2c -->|No| C2e[No Work Orders Message]
    
    C2d --> C2f[Customer Information]
    C2f --> C2g[BOM Items List]
    C2g --> C2h[Select Item for Scheduling]
    C2h --> C2i[Item Scheduling Details]
    
    C2i --> C2j[Machine Selection]
    C2j --> C2k[Process Selection]
    C2k --> C2l[Batch Configuration]
    C2l --> C2m[Date & Time Scheduling]
    C2m --> C2n[Quantity Planning]
    C2n --> C2o[Operator Assignment]
    C2o --> C2p[Incharge Assignment]
    C2p --> C2q[Shift Planning]
    C2q --> C2r{Split Scheduling?}
    C2r -->|Yes| C2s[BOM Split View]
    C2r -->|No| C2t[Add to Schedule]
    
    C2s --> C2u[Component Scheduling]
    C2u --> C2v[Calculate Quantities]
    C2v --> C2t
    C2t --> C2w[Schedule Validation]
    C2w --> C2x{Valid Schedule?}
    C2x -->|Yes| C2y[Save Job Schedule]
    C2x -->|No| C2z[Show Errors]
    C2z --> C2i
    C2y --> C2aa[Update Schedule Tables]
    C2aa --> C2bb[Generate Job Numbers]
    C2bb --> C2cc[Release Schedule]

    %% Schedule Output Flow
    C3 --> C3a[Output Recording]
    C3a --> C3b[Work Order Selection]
    C3b --> C3c[Item Selection]
    C3c --> C3d[Scheduled Jobs List]
    C3d --> C3e[Select Job for Output]
    C3e --> C3f[Output Quantity Entry]
    C3f --> C3g[Quality Check]
    C3g --> C3h[UOM Verification]
    C3h --> C3i[Completion Status]
    C3i --> C3j{Job Complete?}
    C3j -->|Yes| C3k[Mark Job Complete]
    C3j -->|Partial| C3l[Update Progress]
    C3k --> C3m[Update Inventory]
    C3l --> C3m
    C3m --> C3n[Generate Output Report]
    C3n --> C3o[Update Schedule Status]

    %% Maintenance Tracking Flow
    C4 --> C4a[Maintenance Dashboard]
    C4a --> C4b[Overdue Machines]
    C4a --> C4c[Due Soon Machines]
    C4a --> C4d[Maintenance History]
    
    C4b --> C4e[Alert Generation]
    C4c --> C4f[Notification System]
    C4d --> C4g[Historical Reports]
    
    C4e --> C4h[Priority Assignment]
    C4f --> C4i[Schedule Planning]
    C4g --> C4j[Trend Analysis]

    %% Reports Section
    D --> D1[Machine Utilization]
    D --> D2[Maintenance Schedule]
    D --> D3[Cost Analysis]
    D --> D4[Insurance Tracking]
    D --> D5[Performance Reports]

    %% Machine Utilization Reports
    D1 --> D1a[Utilization Metrics]
    D1a --> D1b[Machine Efficiency]
    D1b --> D1c[Downtime Analysis]
    D1c --> D1d[Productivity Reports]
    D1d --> D1e[Capacity Planning]

    %% Maintenance Schedule Reports
    D2 --> D2a[PM Schedule]
    D2a --> D2b[Maintenance Calendar]
    D2b --> D2c[Cost Tracking]
    D2c --> D2d[Spare Parts Usage]
    D2d --> D2e[Engineer Performance]

    %% Cost Analysis Reports
    D3 --> D3a[Machine Cost Analysis]
    D3a --> D3b[Maintenance Costs]
    D3b --> D3c[ROI Analysis]
    D3c --> D3d[Depreciation Reports]
    D3d --> D3e[Budget Planning]

    %% Insurance Tracking
    D4 --> D4a[Insurance Status]
    D4a --> D4b[Expiry Alerts]
    D4b --> D4c[Renewal Tracking]
    D4c --> D4d[Premium Analysis]
    D4d --> D4e[Coverage Reports]

    %% Performance Reports
    D5 --> D5a[OEE Calculation]
    D5a --> D5b[Availability Metrics]
    D5b --> D5c[Quality Metrics]
    D5c --> D5d[Performance Trends]
    D5d --> D5e[Benchmark Analysis]

    %% Dashboard Section
    E --> E1[Machine Status Overview]
    E --> E2[Maintenance Alerts]
    E --> E3[Schedule Summary]
    E --> E4[Key Metrics]
    E --> E5[Quick Actions]

    %% Machine Status Overview
    E1 --> E1a[Active Machines]
    E1a --> E1b[Idle Machines]
    E1b --> E1c[Under Maintenance]
    E1c --> E1d[Out of Service]

    %% Maintenance Alerts
    E2 --> E2a[Overdue Maintenance]
    E2a --> E2b[Due This Week]
    E2b --> E2c[Insurance Expiry]
    E2c --> E2d[Warranty Expiry]

    %% Schedule Summary
    E3 --> E3a[Today's Schedule]
    E3a --> E3b[Pending Jobs]
    E3b --> E3c[Completed Jobs]
    E3c --> E3d[Delayed Jobs]

    %% Key Metrics
    E4 --> E4a[Overall Efficiency]
    E4a --> E4b[Utilization Rate]
    E4b --> E4c[Maintenance Cost]
    E4c --> E4d[Downtime Hours]

    %% Quick Actions
    E5 --> E5a[Add New Machine]
    E5a --> E5b[Schedule Maintenance]
    E5b --> E5c[Create Job Schedule]
    E5c --> E5d[Record Output]

    %% Styling
    classDef masterClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef transactionClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef reportClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dashboardClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef processClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px

    class B,B1,B2,B3 masterClass
    class C,C1,C2,C3,C4 transactionClass
    class D,D1,D2,D3,D4,D5 reportClass
    class E,E1,E2,E3,E4,E5 dashboardClass
    class B1b,B1p,C1d,C1i,C2c,C2r,C2x,C3j processClass
    class B1b,B1p,C1d,C1i,C2c,C2r,C2x,C3j decisionClass
