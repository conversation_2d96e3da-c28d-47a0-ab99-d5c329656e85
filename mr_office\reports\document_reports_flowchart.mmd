graph TD
    A[Document Reports] --> B[Document Statistics]
    A --> C[Usage Analytics]
    A --> D[Storage Reports]
    A --> E[Activity Monitoring]
    A --> F[Compliance Reports]

    %% Document Statistics
    B --> B1[Total Documents Report]
    B --> B2[Documents by Module Report]
    B --> B3[Documents by Type Report]
    B --> B4[Upload Trends Report]
    B --> B5[Size Distribution Report]

    %% Total Documents Report
    B1 --> B1a[Count All Documents]
    B1a --> B1b[Apply Company Filter]
    B1b --> B1c[Apply Date Range]
    B1c --> B1d[Calculate Total Count]
    B1d --> B1e[Generate Summary]
    B1e --> B1f[Display Total Documents]
    B1f --> B1g[Show Growth Rate]
    B1g --> B1h[Export Report]

    %% Documents by Module Report
    B2 --> B2a[Group by Module]
    B2a --> B2b[Count per Module]
    B2b --> B2c[Get Module Names]
    B2c --> B2d[Calculate Percentages]
    B2d --> B2e[Sort by Count]
    B2e --> B2f[Generate Pie Chart]
    B2f --> B2g[Generate Bar Chart]
    B2g --> B2h[Create Module Summary]
    B2h --> B2i[Export Module Report]

    %% Documents by Type Report
    B3 --> B3a[Analyze File Types]
    B3a --> B3b[Group by Content Type]
    B3b --> B3c[Count by Extension]
    B3c --> B3d[Calculate Type Distribution]
    B3d --> B3e[Identify Popular Types]
    B3e --> B3f[Generate Type Chart]
    B3f --> B3g[Show Type Statistics]
    B3g --> B3h[Export Type Report]

    %% Upload Trends Report
    B4 --> B4a[Analyze Upload Patterns]
    B4a --> B4b[Group by Date]
    B4b --> B4c[Calculate Daily Uploads]
    B4c --> B4d[Calculate Weekly Trends]
    B4d --> B4e[Calculate Monthly Trends]
    B4e --> B4f[Identify Peak Periods]
    B4f --> B4g[Generate Trend Chart]
    B4g --> B4h[Forecast Future Uploads]
    B4h --> B4i[Export Trend Report]

    %% Size Distribution Report
    B5 --> B5a[Analyze File Sizes]
    B5a --> B5b[Categorize by Size]
    B5b --> B5c[Calculate Size Ranges]
    B5c --> B5d[Identify Large Files]
    B5d --> B5e[Calculate Storage Impact]
    B5e --> B5f[Generate Size Chart]
    B5f --> B5g[Show Size Statistics]
    B5g --> B5h[Export Size Report]

    %% Usage Analytics
    C --> C1[Download Statistics]
    C --> C2[Popular Documents Report]
    C --> C3[User Activity Report]
    C --> C4[Access Patterns Report]
    C --> C5[Usage Trends Report]

    %% Download Statistics
    C1 --> C1a[Track Download Events]
    C1a --> C1b[Count Downloads per Document]
    C1b --> C1c[Calculate Download Frequency]
    C1c --> C1d[Identify Most Downloaded]
    C1d --> C1e[Analyze Download Patterns]
    C1e --> C1f[Generate Download Chart]
    C1f --> C1g[Show Download Statistics]
    C1g --> C1h[Export Download Report]

    %% Popular Documents Report
    C2 --> C2a[Rank by Downloads]
    C2a --> C2b[Rank by Views]
    C2b --> C2c[Calculate Popularity Score]
    C2c --> C2d[Identify Top Documents]
    C2d --> C2e[Analyze Popularity Trends]
    C2e --> C2f[Generate Popularity Chart]
    C2f --> C2g[Show Popular Documents]
    C2g --> C2h[Export Popularity Report]

    %% User Activity Report
    C3 --> C3a[Track User Actions]
    C3a --> C3b[Count Uploads per User]
    C3b --> C3c[Count Downloads per User]
    C3c --> C3d[Calculate User Engagement]
    C3d --> C3e[Identify Active Users]
    C3e --> C3f[Generate Activity Chart]
    C3f --> C3g[Show User Statistics]
    C3g --> C3h[Export Activity Report]

    %% Access Patterns Report
    C4 --> C4a[Analyze Access Times]
    C4a --> C4b[Track Peak Hours]
    C4b --> C4c[Identify Usage Patterns]
    C4c --> C4d[Calculate Access Frequency]
    C4d --> C4e[Analyze Seasonal Trends]
    C4e --> C4f[Generate Pattern Chart]
    C4f --> C4g[Show Access Patterns]
    C4g --> C4h[Export Pattern Report]

    %% Usage Trends Report
    C5 --> C5a[Historical Usage Analysis]
    C5a --> C5b[Compare Time Periods]
    C5b --> C5c[Calculate Growth Rates]
    C5c --> C5d[Identify Trends]
    C5d --> C5e[Forecast Future Usage]
    C5e --> C5f[Generate Trend Analysis]
    C5f --> C5g[Show Usage Trends]
    C5g --> C5h[Export Trend Report]

    %% Storage Reports
    D --> D1[Storage Utilization Report]
    D --> D2[Storage by Module Report]
    D --> D3[File Size Analysis Report]
    D --> D4[Growth Projections Report]
    D --> D5[Cleanup Recommendations Report]

    %% Storage Utilization Report
    D1 --> D1a[Calculate Total Storage]
    D1a --> D1b[Calculate Used Storage]
    D1b --> D1c[Calculate Available Storage]
    D1c --> D1d[Calculate Utilization Percentage]
    D1d --> D1e[Identify Storage Trends]
    D1e --> D1f[Generate Storage Chart]
    D1f --> D1g[Show Storage Statistics]
    D1g --> D1h[Export Storage Report]

    %% Storage by Module Report
    D2 --> D2a[Calculate Storage per Module]
    D2a --> D2b[Rank Modules by Storage]
    D2b --> D2c[Calculate Module Percentages]
    D2c --> D2d[Identify Storage-heavy Modules]
    D2d --> D2e[Generate Module Storage Chart]
    D2e --> D2f[Show Module Storage]
    D2f --> D2g[Export Module Storage Report]

    %% File Size Analysis Report
    D3 --> D3a[Analyze File Size Distribution]
    D3a --> D3b[Identify Large Files]
    D3b --> D3c[Calculate Average File Size]
    D3c --> D3d[Find Size Outliers]
    D3d --> D3e[Analyze Size Impact]
    D3e --> D3f[Generate Size Analysis]
    D3f --> D3g[Show Size Analysis]
    D3g --> D3h[Export Size Analysis Report]

    %% Growth Projections Report
    D4 --> D4a[Analyze Historical Growth]
    D4a --> D4b[Calculate Growth Rate]
    D4b --> D4c[Project Future Storage Needs]
    D4c --> D4d[Estimate Capacity Requirements]
    D4d --> D4e[Identify Scaling Points]
    D4e --> D4f[Generate Growth Projections]
    D4f --> D4g[Show Growth Forecast]
    D4g --> D4h[Export Growth Report]

    %% Cleanup Recommendations Report
    D5 --> D5a[Identify Unused Documents]
    D5a --> D5b[Find Duplicate Files]
    D5b --> D5c[Identify Old Documents]
    D5c --> D5d[Calculate Cleanup Potential]
    D5d --> D5e[Prioritize Cleanup Actions]
    D5e --> D5f[Generate Cleanup Plan]
    D5f --> D5g[Show Cleanup Recommendations]
    D5g --> D5h[Export Cleanup Report]

    %% Activity Monitoring
    E --> E1[Upload Activity Report]
    E --> E2[Download Activity Report]
    E --> E3[User Actions Report]
    E --> E4[System Events Report]
    E --> E5[Audit Trail Report]

    %% Upload Activity Report
    E1 --> E1a[Track Upload Events]
    E1a --> E1b[Monitor Upload Success/Failure]
    E1b --> E1c[Analyze Upload Patterns]
    E1c --> E1d[Identify Upload Issues]
    E1d --> E1e[Calculate Upload Metrics]
    E1e --> E1f[Generate Upload Activity Chart]
    E1f --> E1g[Show Upload Statistics]
    E1g --> E1h[Export Upload Activity Report]

    %% Download Activity Report
    E2 --> E2a[Track Download Events]
    E2a --> E2b[Monitor Download Success/Failure]
    E2b --> E2c[Analyze Download Patterns]
    E2c --> E2d[Identify Download Issues]
    E2d --> E2e[Calculate Download Metrics]
    E2e --> E2f[Generate Download Activity Chart]
    E2f --> E2g[Show Download Statistics]
    E2g --> E2h[Export Download Activity Report]

    %% User Actions Report
    E3 --> E3a[Log User Actions]
    E3a --> E3b[Track User Behavior]
    E3b --> E3c[Analyze Action Patterns]
    E3c --> E3d[Identify User Trends]
    E3d --> E3e[Calculate Action Metrics]
    E3e --> E3f[Generate User Action Chart]
    E3f --> E3g[Show User Actions]
    E3g --> E3h[Export User Actions Report]

    %% System Events Report
    E4 --> E4a[Monitor System Events]
    E4a --> E4b[Track System Performance]
    E4b --> E4c[Analyze System Health]
    E4c --> E4d[Identify System Issues]
    E4d --> E4e[Calculate System Metrics]
    E4e --> E4f[Generate System Events Chart]
    E4f --> E4g[Show System Events]
    E4g --> E4h[Export System Events Report]

    %% Audit Trail Report
    E5 --> E5a[Comprehensive Audit Log]
    E5a --> E5b[Track All Activities]
    E5b --> E5c[Maintain Audit Trail]
    E5c --> E5d[Ensure Data Integrity]
    E5d --> E5e[Generate Audit Report]
    E5e --> E5f[Show Audit Trail]
    E5f --> E5g[Export Audit Report]

    %% Compliance Reports
    F --> F1[Data Retention Report]
    F --> F2[Access Control Report]
    F --> F3[Security Compliance Report]
    F --> F4[Regulatory Compliance Report]
    F --> F5[Privacy Compliance Report]

    %% Data Retention Report
    F1 --> F1a[Check Retention Policies]
    F1a --> F1b[Identify Expired Documents]
    F1b --> F1c[Calculate Retention Status]
    F1c --> F1d[Generate Retention Report]
    F1d --> F1e[Show Retention Status]
    F1e --> F1f[Export Retention Report]

    %% Access Control Report
    F2 --> F2a[Audit Access Permissions]
    F2a --> F2b[Verify User Access Rights]
    F2b --> F2c[Check Role Assignments]
    F2c --> F2d[Generate Access Report]
    F2d --> F2e[Show Access Control Status]
    F2e --> F2f[Export Access Report]

    %% Security Compliance Report
    F3 --> F3a[Check Security Measures]
    F3a --> F3b[Verify Encryption Status]
    F3b --> F3c[Audit Security Events]
    F3c --> F3d[Generate Security Report]
    F3d --> F3e[Show Security Status]
    F3e --> F3f[Export Security Report]

    %% Regulatory Compliance Report
    F4 --> F4a[Check Regulatory Requirements]
    F4a --> F4b[Verify Compliance Status]
    F4b --> F4c[Audit Regulatory Events]
    F4c --> F4d[Generate Compliance Report]
    F4d --> F4e[Show Compliance Status]
    F4e --> F4f[Export Compliance Report]

    %% Privacy Compliance Report
    F5 --> F5a[Check Privacy Policies]
    F5a --> F5b[Verify Data Protection]
    F5b --> F5c[Audit Privacy Events]
    F5c --> F5d[Generate Privacy Report]
    F5d --> F5e[Show Privacy Status]
    F5e --> F5f[Export Privacy Report]

    %% Report Export Options
    A --> G[Report Export Options]
    G --> G1[PDF Export]
    G --> G2[Excel Export]
    G --> G3[CSV Export]
    G --> G4[JSON Export]
    G --> G5[Email Reports]

    %% Automated Reporting
    A --> H[Automated Reporting]
    H --> H1[Scheduled Reports]
    H --> H2[Report Subscriptions]
    H --> H3[Alert-based Reports]
    H --> H4[Dashboard Integration]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef statisticsClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef analyticsClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef storageClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef activityClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef complianceClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef exportClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px

    class A startClass
    class B,B1,B2,B3,B4,B5 statisticsClass
    class C,C1,C2,C3,C4,C5 analyticsClass
    class D,D1,D2,D3,D4,D5 storageClass
    class E,E1,E2,E3,E4,E5 activityClass
    class F,F1,F2,F3,F4,F5 complianceClass
    class G,G1,G2,G3,G4,G5,H,H1,H2,H3,H4 exportClass
