graph TD
    A[Scheduler Menu] --> B[Page Load & Initialization]
    A --> C[Schedule Creation]
    A --> D[Schedule Management]
    A --> E[Schedule Optimization]

    %% Page Load & Initialization
    B --> B1[Load Scheduling Page]
    B1 --> B2[Initialize Calendar Interface]
    B2 --> B3[Load Work Orders]
    B3 --> B4[Load Resource Data]
    B4 --> B5[Load Machine Availability]
    B5 --> B6[Load Operator Schedules]
    B6 --> B7[Setup Drag-Drop Interface]
    B7 --> B8[Display Current Schedules]
    B8 --> B9[Page Ready for Scheduling]

    %% Schedule Creation
    C --> C1[Work Order Selection]
    C --> C2[Resource Allocation]
    C --> C3[Time Slot Assignment]
    C --> C4[Schedule Validation]

    %% Work Order Selection
    C1 --> C1a[Load Pending Work Orders]
    C1a --> C1b[Filter by Priority]
    C1b --> C1c[Filter by Due Date]
    C1c --> C1d[Filter by Customer]
    C1d --> C1e[Select Work Order]
    C1e --> C1f{Work Order Selected?}
    C1f -->|No| C1g[Work Order Selection Required]
    C1f -->|Yes| C1h[Load Work Order Details]
    
    C1g --> C1e
    C1h --> C1i[Display BOM Items]
    C1i --> C1j[Show Required Operations]
    C1j --> C1k[Calculate Time Requirements]
    C1k --> C1l[Identify Resource Needs]
    C1l --> C2

    %% Resource Allocation
    C2 --> C2a[Machine Selection]
    C2a --> C2b[Load Available Machines]
    C2b --> C2c[Check Machine Capability]
    C2c --> C2d[Check Machine Availability]
    C2d --> C2e{Machine Available?}
    C2e -->|No| C2f[Show Alternative Machines]
    C2e -->|Yes| C2g[Assign Machine]
    
    C2f --> C2b
    C2g --> C2h[Operator Assignment]
    C2h --> C2i[Load Available Operators]
    C2i --> C2j[Check Operator Skills]
    C2j --> C2k[Check Operator Availability]
    C2k --> C2l{Operator Available?}
    C2l -->|No| C2m[Show Alternative Operators]
    C2l -->|Yes| C2n[Assign Operator]
    
    C2m --> C2i
    C2n --> C2o[Material Allocation]
    C2o --> C2p[Check Material Availability]
    C2p --> C2q{Material Available?}
    C2q -->|No| C2r[Material Shortage Alert]
    C2q -->|Yes| C2s[Reserve Materials]
    
    C2r --> C2t[Suggest Alternative Schedule]
    C2s --> C3

    %% Time Slot Assignment
    C3 --> C3a[Calendar Interface]
    C3a --> C3b[Select Start Date/Time]
    C3b --> C3c[Calculate End Time]
    C3c --> C3d[Check Time Conflicts]
    C3d --> C3e{Time Slot Available?}
    C3e -->|No| C3f[Show Conflict Details]
    C3e -->|Yes| C3g[Assign Time Slot]
    
    C3f --> C3h[Suggest Alternative Times]
    C3h --> C3b
    C3g --> C3i[Set Break Times]
    C3i --> C3j[Set Shift Boundaries]
    C3j --> C3k[Calculate Buffer Time]
    C3k --> C3l[Set Priority Level]
    C3l --> C4

    %% Schedule Validation
    C4 --> C4a[Validate Schedule]
    C4a --> C4b[Check Resource Conflicts]
    C4b --> C4c[Check Time Constraints]
    C4c --> C4d[Check Capacity Limits]
    C4d --> C4e[Check Dependencies]
    C4e --> C4f{Validation Passed?}
    C4f -->|No| C4g[Show Validation Errors]
    C4f -->|Yes| C4h[Save Schedule]
    
    C4g --> C1
    C4h --> C4i[Generate Schedule ID]
    C4i --> C4j[Set System Information]
    C4j --> C4k[Set Company ID]
    C4k --> C4l[Set Session Details]
    C4l --> C4m[Set Financial Year]
    C4m --> C4n[Execute Database Insert]
    C4n --> C4o{Insert Successful?}
    C4o -->|No| C4p[Database Error Message]
    C4o -->|Yes| C4q[Schedule Creation Success]
    
    C4p --> C1
    C4q --> C4r[Update Resource Status]
    C4r --> C4s[Send Notifications]
    C4s --> C4t[Refresh Schedule Display]
    C4t --> C4u[Return to Schedule View]

    %% Schedule Management
    D --> D1[Schedule Viewing]
    D --> D2[Schedule Modification]
    D --> D3[Schedule Tracking]
    D --> D4[Schedule Reports]

    %% Schedule Viewing
    D1 --> D1a[Calendar View]
    D1a --> D1b[Daily View]
    D1b --> D1c[Weekly View]
    D1c --> D1d[Monthly View]
    D1d --> D1e[Resource View]
    D1e --> D1f[Gantt Chart View]
    D1f --> D1g[Timeline View]
    D1g --> D1h[Filter Options]
    D1h --> D1i[Search Functionality]
    D1i --> D1j[Export Options]

    %% Schedule Modification
    D2 --> D2a[Select Schedule for Edit]
    D2a --> D2b[Check Schedule Status]
    D2b --> D2c{Schedule Editable?}
    D2c -->|No| D2d[Schedule Cannot be Modified]
    D2c -->|Yes| D2e[Load Schedule Details]
    
    D2d --> D1
    D2e --> D2f[Modify Time Slots]
    D2f --> D2g[Change Resource Assignment]
    D2g --> D2h[Update Priority]
    D2h --> D2i[Adjust Dependencies]
    D2i --> D2j[Validate Changes]
    D2j --> D2k{Changes Valid?}
    D2k -->|No| D2l[Show Validation Errors]
    D2k -->|Yes| D2m[Update Schedule]
    
    D2l --> D2f
    D2m --> D2n[Set Modified Information]
    D2n --> D2o[Set Modified Date/Time]
    D2o --> D2p[Set Modified By User]
    D2p --> D2q[Execute Database Update]
    D2q --> D2r{Update Successful?}
    D2r -->|No| D2s[Update Error Message]
    D2r -->|Yes| D2t[Update Success Message]
    
    D2s --> D2f
    D2t --> D2u[Send Change Notifications]
    D2u --> D2v[Refresh Schedule Display]
    D2v --> D1

    %% Schedule Tracking
    D3 --> D3a[Progress Monitoring]
    D3a --> D3b[Status Updates]
    D3b --> D3c[Delay Tracking]
    D3c --> D3d[Completion Tracking]
    D3d --> D3e[Performance Metrics]
    D3e --> D3f[Alert Generation]
    D3f --> D3g[Escalation Management]
    D3g --> D3h[Real-time Updates]

    %% Schedule Reports
    D4 --> D4a[Schedule Summary Report]
    D4a --> D4b[Resource Utilization Report]
    D4b --> D4c[Efficiency Report]
    D4c --> D4d[Delay Analysis Report]
    D4d --> D4e[Performance Report]
    D4e --> D4f[Capacity Report]
    D4f --> D4g[Export Report Options]

    %% Schedule Optimization
    E --> E1[Automatic Optimization]
    E --> E2[Manual Optimization]
    E --> E3[Constraint Management]
    E --> E4[Performance Analysis]

    %% Automatic Optimization
    E1 --> E1a[Load Optimization Engine]
    E1a --> E1b[Analyze Current Schedules]
    E1b --> E1c[Identify Bottlenecks]
    E1c --> E1d[Calculate Optimal Allocation]
    E1d --> E1e[Generate Optimization Suggestions]
    E1e --> E1f[Apply Optimization]
    E1f --> E1g[Validate Optimized Schedule]
    E1g --> E1h{Optimization Valid?}
    E1h -->|No| E1i[Revert to Original]
    E1h -->|Yes| E1j[Save Optimized Schedule]
    
    E1i --> E1b
    E1j --> E1k[Update Resource Assignments]
    E1k --> E1l[Send Optimization Notifications]

    %% Manual Optimization
    E2 --> E2a[Optimization Interface]
    E2a --> E2b[Drag-Drop Scheduling]
    E2b --> E2c[Resource Reallocation]
    E2c --> E2d[Time Adjustment]
    E2d --> E2e[Priority Reordering]
    E2e --> E2f[Dependency Management]
    E2f --> E2g[Validate Manual Changes]
    E2g --> E2h{Changes Valid?}
    E2h -->|No| E2i[Show Validation Errors]
    E2h -->|Yes| E2j[Apply Manual Optimization]
    
    E2i --> E2b
    E2j --> E2k[Save Manual Changes]
    E2k --> E2l[Update Displays]

    %% Constraint Management
    E3 --> E3a[Time Constraints]
    E3a --> E3b[Resource Constraints]
    E3b --> E3c[Capacity Constraints]
    E3c --> E3d[Dependency Constraints]
    E3d --> E3e[Priority Constraints]
    E3e --> E3f[Custom Constraints]
    E3f --> E3g[Constraint Validation]
    E3g --> E3h[Constraint Enforcement]

    %% Performance Analysis
    E4 --> E4a[Schedule Efficiency Analysis]
    E4a --> E4b[Resource Utilization Analysis]
    E4b --> E4c[Throughput Analysis]
    E4c --> E4d[Bottleneck Analysis]
    E4d --> E4e[Cost Analysis]
    E4e --> E4f[ROI Analysis]
    E4f --> E4g[Improvement Recommendations]

    %% Error Handling & Validation
    A --> F[Error Handling]
    F --> F1[Schedule Conflicts]
    F1 --> F1a[Resource Double Booking]
    F1a --> F1b[Time Overlap Conflicts]
    F1b --> F1c[Capacity Overload]
    F1c --> F1d[Dependency Violations]
    
    F --> F2[System Errors]
    F2 --> F2a[Database Connection Errors]
    F2a --> F2b[Calculation Errors]
    F2b --> F2c[Integration Errors]
    
    F --> F3[User Errors]
    F3 --> F3a[Invalid Input Errors]
    F3a --> F3b[Permission Errors]
    F3b --> F3c[Session Timeout Errors]

    %% Real-time Features
    A --> G[Real-time Features]
    G --> G1[Live Schedule Updates]
    G1 --> G2[Real-time Notifications]
    G2 --> G3[Dynamic Resource Status]
    G3 --> G4[Instant Conflict Detection]
    G4 --> G5[Live Performance Metrics]

    %% Integration Features
    A --> H[Integration Features]
    H --> H1[Work Order Integration]
    H1 --> H2[Resource Management Integration]
    H2 --> H3[Inventory Integration]
    H3 --> H4[HR Integration]
    H4 --> H5[Quality Control Integration]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef initClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef creationClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef managementClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef optimizationClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef decisionClass fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A startClass
    class B,B1,B2,B9 initClass
    class C,C1,C2,C3,C4 creationClass
    class D,D1,D2,D3,D4 managementClass
    class E,E1,E2,E3,E4 optimizationClass
    class C1f,C2e,C2l,C2q,C3e,C4f,C4o,D2c,D2k,D2r,E1h,E2h decisionClass
    class C1g,C2f,C2m,C2r,C3f,C4g,C4p,D2d,D2l,D2s,E1i,E2i errorClass
    class C4q,D2t,E1j,E2j successClass
