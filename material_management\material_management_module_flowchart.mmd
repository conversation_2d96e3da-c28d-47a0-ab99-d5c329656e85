graph TD
    A[Material Management Module] --> B[Masters]
    A --> C[Transactions]
    A --> D[Reports]

    %% Masters Section
    B --> B1[Business Nature]
    B --> B2[Business Type]
    B --> B3[Service Coverage]
    B --> B4[Buyer]
    B --> B5[Supplier]
    B --> B6[Set Rate]

    %% Business Nature Flow
    B1 --> B1a[Business Nature Management]
    B1a --> B1b[Add New Business Nature]
    B1b --> B1c[Edit Business Nature]
    B1c --> B1d[Delete Business Nature]
    B1d --> B1e[View Business Nature List]

    %% Business Type Flow
    B2 --> B2a[Business Type Management]
    B2a --> B2b[Add New Business Type]
    B2b --> B2c[Edit Business Type]
    B2c --> B2d[Delete Business Type]
    B2d --> B2e[View Business Type List]

    %% Service Coverage Flow
    B3 --> B3a[Service Coverage Management]
    B3a --> B3b[Add New Service Coverage]
    B3b --> B3c[Edit Service Coverage]
    B3c --> B3d[Delete Service Coverage]
    B3d --> B3e[View Service Coverage List]

    %% Buyer Flow
    B4 --> B4a[Buyer Management]
    B4a --> B4b[Add New Buyer]
    B4b --> B4c[Edit Buyer]
    B4c --> B4d[Delete Buyer]
    B4d --> B4e[View Buyer List]

    %% Supplier Flow
    B5 --> B5a[Supplier Master Management]
    B5a --> B5b[New Supplier Registration]
    B5b --> B5c[Edit Supplier Details]
    B5c --> B5d[Delete Supplier]
    B5d --> B5e[Print Supplier Details]
    B5e --> B5f[View Supplier List]

    %% Set Rate Flow
    B6 --> B6a[Rate Set Management]
    B6a --> B6b[Create New Rate Set]
    B6b --> B6c[Edit Rate Set]
    B6c --> B6d[View Rate Set Details]
    B6d --> B6e[Rate Set List]

    %% Transactions Section
    C --> C1[Rate Lock/UnLock]
    C --> C2[Purchase Requisition PR]
    C --> C3[Special Purpose Requisition SPR]
    C --> C4[Check SPR]
    C --> C5[Approve SPR]
    C --> C6[Authorize SPR]
    C --> C7[Purchase Order PO]
    C --> C8[Check PO]
    C --> C9[Approve PO]
    C --> C10[Authorize PO]

    %% Rate Lock/UnLock Flow
    C1 --> C1a[Rate Lock/UnLock Management]
    C1a --> C1b[Lock Item Rates]
    C1b --> C1c[Unlock Item Rates]
    C1c --> C1d[View Lock Status]
    C1d --> C1e[Rate Lock History]

    %% Purchase Requisition Flow
    C2 --> C2a[PR Management]
    C2a --> C2b[Create New PR]
    C2b --> C2c[Edit PR]
    C2c --> C2d[Delete PR]
    C2d --> C2e[Print PR]
    C2e --> C2f[PR Dashboard]

    %% Special Purpose Requisition Flow
    C3 --> C3a[SPR Management]
    C3a --> C3b[Create New SPR]
    C3b --> C3c[Edit SPR]
    C3c --> C3d[Delete SPR]
    C3d --> C3e[Print SPR]
    C3e --> C3f[SPR Dashboard]

    %% Check SPR Flow
    C4 --> C4a[SPR Checking Process]
    C4a --> C4b[Review SPR Details]
    C4b --> C4c[Verify Requirements]
    C4c --> C4d[Check Approval]
    C4d --> C4e[Update Check Status]

    %% Approve SPR Flow
    C5 --> C5a[SPR Approval Process]
    C5a --> C5b[Review Checked SPR]
    C5b --> C5c[Approve/Reject SPR]
    C5c --> C5d[Add Approval Comments]
    C5d --> C5e[Update Approval Status]

    %% Authorize SPR Flow
    C6 --> C6a[SPR Authorization Process]
    C6a --> C6b[Review Approved SPR]
    C6b --> C6c[Authorize/Reject SPR]
    C6c --> C6d[Final Authorization]
    C6d --> C6e[Update Authorization Status]

    %% Purchase Order Flow
    C7 --> C7a[PO Management]
    C7a --> C7b[Create New PO]
    C7b --> C7c[Edit PO]
    C7c --> C7d[Delete PO]
    C7d --> C7e[Print PO]
    C7e --> C7f[PO Dashboard]

    %% Check PO Flow
    C8 --> C8a[PO Checking Process]
    C8a --> C8b[Review PO Details]
    C8b --> C8c[Verify Supplier Info]
    C8c --> C8d[Check Terms & Conditions]
    C8d --> C8e[Update Check Status]

    %% Approve PO Flow
    C9 --> C9a[PO Approval Process]
    C9a --> C9b[Review Checked PO]
    C9b --> C9c[Approve/Reject PO]
    C9c --> C9d[Add Approval Comments]
    C9d --> C9e[Update Approval Status]

    %% Authorize PO Flow
    C10 --> C10a[PO Authorization Process]
    C10a --> C10b[Review Approved PO]
    C10b --> C10c[Authorize/Reject PO]
    C10c --> C10d[Final Authorization]
    C10d --> C10e[Update Authorization Status]

    %% Reports Section
    D --> D1[Rate Register]
    D --> D2[Rate Lock/UnLock Report]
    D --> D3[Supplier Rating]
    D --> D4[Material Forecasting]
    D --> D5[Inward/Outward Register]
    D --> D6[Search]

    %% Rate Register Flow
    D1 --> D1a[Rate Register Report]
    D1a --> D1b[Filter by Date Range]
    D1b --> D1c[Filter by Item]
    D1c --> D1d[Filter by Supplier]
    D1d --> D1e[Generate Rate Report]
    D1e --> D1f[Export Rate Register]

    %% Rate Lock/UnLock Report Flow
    D2 --> D2a[Rate Lock Report]
    D2a --> D2b[View Lock Status]
    D2b --> D2c[Lock History]
    D2c --> D2d[Unlock History]
    D2d --> D2e[Generate Lock Report]

    %% Supplier Rating Flow
    D3 --> D3a[Supplier Rating Report]
    D3a --> D3b[Quality Rating]
    D3b --> D3c[Delivery Rating]
    D3c --> D3d[Service Rating]
    D3d --> D3e[Overall Rating]
    D3e --> D3f[Generate Rating Report]

    %% Material Forecasting Flow
    D4 --> D4a[Material Forecasting Report]
    D4a --> D4b[Demand Analysis]
    D4b --> D4c[Supply Analysis]
    D4c --> D4d[Forecast Calculation]
    D4d --> D4e[Generate Forecast Report]

    %% Inward/Outward Register Flow
    D5 --> D5a[Inward/Outward Register]
    D5a --> D5b[Inward Transactions]
    D5b --> D5c[Outward Transactions]
    D5c --> D5d[Transaction Summary]
    D5d --> D5e[Generate Register Report]

    %% Search Flow
    D6 --> D6a[Advanced Search]
    D6a --> D6b[Search Criteria]
    D6b --> D6c[Search Results]
    D6c --> D6d[Export Search Results]

    %% Integration Points
    A --> E[Integration Points]
    E --> E1[Design Module Integration]
    E --> E2[Inventory Module Integration]
    E --> E3[Accounts Module Integration]
    E --> E4[Project Management Integration]

    %% Workflow Management
    A --> F[Workflow Management]
    F --> F1[Approval Workflows]
    F --> F2[Authorization Workflows]
    F --> F3[Notification System]
    F --> F4[Status Tracking]

    %% Styling
    classDef startClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef masterClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef transactionClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef reportClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef integrationClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef workflowClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A startClass
    class B,B1,B2,B3,B4,B5,B6 masterClass
    class C,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10 transactionClass
    class D,D1,D2,D3,D4,D5,D6 reportClass
    class E,E1,E2,E3,E4 integrationClass
    class F,F1,F2,F3,F4 workflowClass
