flowchart TD
    A[HR Module Entry Point] --> B{User Authentication}
    B -->|Failed| C[Redirect to Login]
    B -->|Success| D[Load HR Dashboard]
    
    D --> E[Initialize HR Context]
    E --> F[Company Context: CompId]
    F --> G[Financial Year Context: FinYearId]
    G --> H[User Session: SessionId]
    H --> I[Display HR Dashboard]
    
    I --> J[HR Module Components]
    J --> K[Masters Management]
    J --> L[Employee Management]
    J --> M[Payroll Management]
    J --> N[Attendance Management]
    J --> O[Communication Systems]
    J --> P[Travel Management]
    J --> Q[Asset Management]
    J --> R[Reports & Analytics]
    
    %% Masters Management
    K --> S[Department Master]
    K --> T[Designation Master]
    K --> U[Grade Master]
    K --> V[Business Group Master]
    K --> W[Holiday Master]
    K --> X[Working Days Master]
    
    S --> Y[CRUD Operations on tblHR_Departments]
    T --> Z[CRUD Operations on tblHR_Designation]
    U --> AA[CRUD Operations on tblHR_Grade]
    V --> BB[CRUD Operations on BusinessGroup]
    W --> CC[CRUD Operations on tblHR_Holiday_Master]
    X --> DD[CRUD Operations on Working Days]
    
    %% Employee Management
    L --> EE[Employee Registration]
    L --> FF[Employee Profile Management]
    L --> GG[Employee Lifecycle]
    L --> HH[Offer Letter Management]
    
    EE --> II[Office Staff Registration]
    II --> JJ[Employee ID Generation]
    JJ --> KK[Personal Information Management]
    KK --> LL[Employment Details Setup]
    LL --> MM[Salary Configuration]
    MM --> NN[Benefits Assignment]
    
    FF --> OO[Profile Updates]
    OO --> PP[Contact Information]
    PP --> QQ[Emergency Contacts]
    QQ --> RR[Document Management]
    RR --> SS[Photo Management]
    
    GG --> TT[Promotion Processing]
    TT --> UU[Transfer Management]
    UU --> VV[Resignation Processing]
    VV --> WW[Increment Management]
    WW --> XX[Performance Reviews]
    
    HH --> YY[Offer Letter Creation]
    YY --> ZZ[Offer Approval Workflow]
    ZZ --> AAA[Offer Letter Printing]
    AAA --> BBB[Employee Onboarding]
    
    %% Payroll Management
    M --> CCC[Salary Processing]
    M --> DDD[Salary Components]
    M --> EEE[Deductions Management]
    M --> FFF[Bank Statement Generation]
    M --> GGG[Payroll Reports]
    
    CCC --> HHH[Monthly Salary Calculation]
    HHH --> III[Basic Salary Computation]
    III --> JJJ[Allowances Calculation]
    JJJ --> KKK[Overtime Calculation]
    KKK --> LLL[Bonus Calculation]
    
    DDD --> MMM[Vehicle Allowance]
    MMM --> NNN[LTA Management]
    NNN --> OOO[Ex-Gratia Processing]
    OOO --> PPP[Loyalty Bonus]
    PPP --> QQQ[Other Allowances]
    
    EEE --> RRR[PF Deductions]
    RRR --> SSS[ESI Deductions]
    SSS --> TTT[Professional Tax]
    TTT --> UUU[Income Tax (TDS)]
    UUU --> VVV[Loan Deductions]
    
    FFF --> WWW[Bank Transfer File]
    WWW --> XXX[NEFT/RTGS Processing]
    XXX --> YYY[Bank Reconciliation]
    
    %% Attendance Management
    N --> ZZZ[Attendance Tracking]
    N --> AAAA[Leave Management]
    N --> BBBB[Overtime Management]
    N --> CCCC[Holiday Management]
    
    ZZZ --> DDDD[Daily Attendance]
    DDDD --> EEEE[Time In/Out Recording]
    EEEE --> FFFF[Attendance Regularization]
    FFFF --> GGGG[Monthly Attendance Summary]
    
    AAAA --> HHHH[Leave Application]
    HHHH --> IIII[Leave Approval Workflow]
    IIII --> JJJJ[Leave Balance Management]
    JJJJ --> KKKK[Leave Encashment]
    
    %% Communication Systems
    O --> LLLL[SMS Management]
    O --> MMMM[News & Notices]
    O --> NNNN[Mobile Bill Management]
    O --> OOOO[Corporate Communication]
    
    LLLL --> PPPP[Bulk SMS Sending]
    PPPP --> QQQQ[SMS Templates]
    QQQQ --> RRRR[SMS Delivery Reports]
    
    MMMM --> SSSS[News Publishing]
    SSSS --> TTTT[Notice Board Management]
    TTTT --> UUUU[Document Attachments]
    UUUU --> VVVV[Publication Scheduling]
    
    NNNN --> WWWW[Mobile Bill Entry]
    WWWW --> XXXX[Bill Approval Workflow]
    XXXX --> YYYY[Reimbursement Processing]
    YYYY --> ZZZZ[Mobile Usage Reports]
    
    %% Travel Management
    P --> AAAAA[Tour Intimation]
    P --> BBBBB[Travel Approval]
    P --> CCCCC[Expense Management]
    P --> DDDDD[Travel Reports]
    
    AAAAA --> EEEEE[Travel Request Creation]
    EEEEE --> FFFFF[Travel Details Entry]
    FFFFF --> GGGGG[Travel Approval Workflow]
    GGGGG --> HHHHH[Travel Authorization]
    
    BBBBB --> IIIII[Multi-level Approval]
    IIIII --> JJJJJ[Budget Validation]
    JJJJJ --> KKKKK[Travel Policy Compliance]
    
    CCCCC --> LLLLL[Expense Entry]
    LLLLL --> MMMMM[Receipt Management]
    MMMMM --> NNNNN[Expense Approval]
    NNNNN --> OOOOO[Reimbursement Processing]
    
    %% Asset Management
    Q --> PPPPP[IT Asset Management]
    Q --> QQQQQ[Office Asset Tracking]
    Q --> RRRRR[Asset Allocation]
    Q --> SSSSS[Asset Maintenance]
    
    PPPPP --> TTTTT[Desktop Management]
    TTTTT --> UUUUU[Laptop Tracking]
    UUUUU --> VVVVV[Printer Management]
    VVVVV --> WWWWW[Network Equipment]
    
    QQQQQ --> XXXXX[Furniture Tracking]
    XXXXX --> YYYYY[Equipment Management]
    YYYYY --> ZZZZZ[Facility Management]
    
    %% Reports & Analytics
    R --> AAAAAA[Employee Reports]
    R --> BBBBBB[Payroll Reports]
    R --> CCCCCC[Attendance Reports]
    R --> DDDDDD[Performance Analytics]
    R --> EEEEEE[Compliance Reports]
    
    AAAAAA --> FFFFFF[Employee Directory]
    FFFFFF --> GGGGGG[Department-wise Reports]
    GGGGGG --> HHHHHH[Employee Status Reports]
    HHHHHH --> IIIIII[Joining/Leaving Reports]
    
    BBBBBB --> JJJJJJ[Salary Summary Reports]
    JJJJJJ --> KKKKKK[Tax Reports]
    KKKKKK --> LLLLLL[PF/ESI Reports]
    LLLLLL --> MMMMMM[Bank Statement Reports]
    
    CCCCCC --> NNNNNN[Daily Attendance Reports]
    NNNNNN --> OOOOOO[Monthly Attendance Summary]
    OOOOOO --> PPPPPP[Leave Reports]
    PPPPPP --> QQQQQQ[Overtime Reports]
    
    %% Data Integration Points
    RRRRRR[Data Integration] --> SSSSSS[Accounts Module Integration]
    RRRRRR --> TTTTTT[MIS Module Integration]
    RRRRRR --> UUUUUU[Project Management Integration]
    RRRRRR --> VVVVVV[Material Management Integration]
    
    SSSSSS --> WWWWWW[Salary Voucher Integration]
    WWWWWW --> XXXXXX[Employee Expense Integration]
    XXXXXX --> YYYYYY[Tax Calculation Integration]
    
    TTTTTT --> ZZZZZZ[Budget Allocation Integration]
    ZZZZZZ --> AAAAAAA[Department Budget Integration]
    AAAAAAA --> BBBBBBB[Cost Center Integration]
    
    %% Business Intelligence Layer
    CCCCCCC[Business Intelligence] --> DDDDDDD[HR Analytics Dashboard]
    DDDDDDD --> EEEEEEE[Employee Metrics]
    EEEEEEE --> FFFFFFF[Payroll Analytics]
    FFFFFFF --> GGGGGGG[Attendance Analytics]
    GGGGGGG --> HHHHHHH[Performance Metrics]
    
    CCCCCCC --> IIIIIII[Predictive Analytics]
    IIIIIII --> JJJJJJJ[Attrition Prediction]
    JJJJJJJ --> KKKKKKK[Salary Trend Analysis]
    KKKKKKK --> LLLLLLL[Performance Forecasting]
    
    %% Workflow Management
    MMMMMMM[Workflow Management] --> NNNNNNN[Approval Workflows]
    NNNNNNN --> OOOOOOO[Multi-level Approvals]
    OOOOOOO --> PPPPPPP[Workflow Notifications]
    PPPPPPP --> QQQQQQQ[Status Tracking]
    QQQQQQQ --> RRRRRRR[Escalation Management]
    
    %% Security & Access Control
    SSSSSSS[Security Layer] --> TTTTTTT[Role-based Access Control]
    TTTTTTT --> UUUUUUU[Module-level Permissions]
    UUUUUUU --> VVVVVVV[Function-level Security]
    VVVVVVV --> WWWWWWW[Data-level Security]
    WWWWWWW --> XXXXXXX[Employee Data Privacy]
    
    SSSSSSS --> YYYYYYY[Audit Trail Management]
    YYYYYYY --> ZZZZZZZ[User Action Logging]
    ZZZZZZZ --> AAAAAAAA[Data Change Tracking]
    AAAAAAAA --> BBBBBBBB[Compliance Audit Support]
    
    %% Django Implementation Architecture
    CCCCCCCC[Django HR Architecture] --> DDDDDDDD[HR Models Layer]
    DDDDDDDD --> EEEEEEEE[Employee Management Models]
    EEEEEEEE --> FFFFFFFF[Payroll Management Models]
    FFFFFFFF --> GGGGGGGG[Attendance Models]
    GGGGGGGG --> HHHHHHHH[Communication Models]
    
    CCCCCCCC --> IIIIIIII[HR Views Layer]
    IIIIIIII --> JJJJJJJJ[Class-based Views]
    JJJJJJJJ --> KKKKKKKK[API Views for HTMX]
    KKKKKKKK --> LLLLLLLL[Report Generation Views]
    LLLLLLLL --> MMMMMMMM[Workflow Management Views]
    
    CCCCCCCC --> NNNNNNNN[HR Forms Layer]
    NNNNNNNN --> OOOOOOOO[Employee Forms with Validation]
    OOOOOOOO --> PPPPPPPP[Payroll Forms]
    PPPPPPPP --> QQQQQQQQ[Attendance Forms]
    QQQQQQQQ --> RRRRRRRR[Communication Forms]
    
    CCCCCCCC --> SSSSSSSS[HR Templates Layer]
    SSSSSSSS --> TTTTTTTT[SAP Fiori UI Templates]
    TTTTTTTT --> UUUUUUUU[Responsive Dashboard Templates]
    UUUUUUUU --> VVVVVVVV[Report Display Templates]
    VVVVVVVV --> WWWWWWWW[Mobile-friendly Templates]
    
    %% Technology Stack
    XXXXXXXX[Technology Stack] --> YYYYYYYY[Backend: Django 5.2]
    YYYYYYYY --> ZZZZZZZZ[Frontend: HTMX + Alpine.js]
    ZZZZZZZZ --> AAAAAAAAA[CSS: Tailwind CSS]
    AAAAAAAAA --> BBBBBBBBB[Database: SQLite with managed=False]
    BBBBBBBBB --> CCCCCCCCC[Charts: Chart.js]
    CCCCCCCCC --> DDDDDDDDD[Icons: Lucide Icons]
    DDDDDDDDD --> EEEEEEEEE[PDF: ReportLab]
    EEEEEEEEE --> FFFFFFFFF[Email: Django Email Backend]
    
    %% Performance Optimization
    GGGGGGGGG[Performance Layer] --> HHHHHHHHH[Database Query Optimization]
    HHHHHHHHH --> IIIIIIIII[Caching Strategy]
    IIIIIIIII --> JJJJJJJJJ[Lazy Loading Implementation]
    JJJJJJJJJ --> KKKKKKKKK[Pagination for Large Datasets]
    KKKKKKKKK --> LLLLLLLLL[Background Task Processing]
    LLLLLLLLL --> MMMMMMMMM[Async Report Generation]
    
    %% Error Handling & Monitoring
    NNNNNNNNN[Error Management] --> OOOOOOOOO[Exception Handling]
    OOOOOOOOO --> PPPPPPPPP[User-friendly Error Messages]
    PPPPPPPPP --> QQQQQQQQQ[System Error Logging]
    QQQQQQQQQ --> RRRRRRRRR[Performance Monitoring]
    RRRRRRRRR --> SSSSSSSSS[Health Check Endpoints]
    SSSSSSSSS --> TTTTTTTTT[Automated Alerts]
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style J fill:#e8f5e8
    style K fill:#fff3e0
    style L fill:#fff3e0
    style M fill:#fff3e0
    style N fill:#fff3e0
    style O fill:#fff3e0
    style P fill:#fff3e0
    style Q fill:#fff3e0
    style R fill:#fff3e0
    style RRRRRR fill:#f1f8e9
    style CCCCCCC fill:#e3f2fd
    style MMMMMMM fill:#e8f5e8
    style SSSSSSS fill:#f3e5f5
    style CCCCCCCC fill:#f1f8e9
    style XXXXXXXX fill:#fafafa
    style GGGGGGGGG fill:#e3f2fd
    style NNNNNNNNN fill:#fce4ec
